﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Common;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.PaymentGuests;

[Route("api/app/payment-guests")]
[Authorize(WismaAppPermissions.PolicyPaymentGuest.Default)]
public class PaymentGuestsAppService : CrudAppService<
    PaymentGuest,
    PaymentGuestsDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdatePaymentGuestsDto,
    CreateUpdatePaymentGuestsDto
>, IPaymentGuestsAppService
{
    private readonly IRepository<PaymentGuest, Guid> _repository;

    public PaymentGuestsAppService(IRepository<PaymentGuest, Guid> repository,
        ILogger<PaymentGuestsAppService> logger)
        : base(repository)
    {
        _repository = repository;
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyPaymentGuest.View)]
    public override Task<PagedResultDto<PaymentGuestsDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyPaymentGuest.View)]
    public override Task<PaymentGuestsDto> GetAsync(Guid id)
    {
        return base.GetAsync(id);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyPaymentGuest.Create)]
    public override Task<PaymentGuestsDto> CreateAsync(CreateUpdatePaymentGuestsDto input)
    {
        return base.CreateAsync(input);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyPaymentGuest.Edit)]
    public override Task<PaymentGuestsDto> UpdateAsync(Guid id, CreateUpdatePaymentGuestsDto input)
    {
        return base.UpdateAsync(id, input);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyPaymentGuest.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }
}