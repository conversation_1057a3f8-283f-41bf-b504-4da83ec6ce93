using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.SettingManagement;
using Volo.Abp.Settings;

namespace Imip.HotelFrontOffice.Settings;

[Authorize(WismaAppPermissions.PolicySettings.Default)]
public class StatusSettingsAppService : HotelFrontOfficeAppService, IStatusSettingsAppService
{
    private readonly ISettingProvider _settingProvider;
    private readonly ISettingManager _settingManager;
    private readonly ILogger<StatusSettingsAppService> _logger;

    public StatusSettingsAppService(
        ISettingProvider settingProvider,
        ISettingManager settingManager,
        ILogger<StatusSettingsAppService> logger)
    {
        _settingProvider = settingProvider;
        _settingManager = settingManager;
        _logger = logger;
    }

    [Authorize(WismaAppPermissions.PolicySettings.View)]
    public async Task<StatusSettingsDto> GetAsync()
    {
        var checkInStatusName = await _settingProvider.GetOrNullAsync(HotelFrontOfficeSettings.ReservationStatus.CheckInStatusName);
        var checkOutStatusName = await _settingProvider.GetOrNullAsync(HotelFrontOfficeSettings.ReservationStatus.CheckOutStatusName);
        var occupiedInStayStatusName = await _settingProvider.GetOrNullAsync(HotelFrontOfficeSettings.SettingRoomStatus.OccupiedInStayStatusName);
        var dirtyStatusName = await _settingProvider.GetOrNullAsync(HotelFrontOfficeSettings.SettingRoomStatus.DirtyStatusName);

        return new StatusSettingsDto
        {
            CheckInStatusName = checkInStatusName ?? "Check IN",
            CheckOutStatusName = checkOutStatusName ?? "Check OUT",
            OccupiedInStayStatusName = occupiedInStayStatusName ?? "OVC IN Stay",
            DirtyStatusName = dirtyStatusName ?? "Dirty"
        };
    }

    [Authorize(WismaAppPermissions.PolicySettings.Edit)]
    public async Task<StatusSettingsDto> UpdateAsync(StatusSettingsDto input)
    {
        // Update all settings
        await _settingManager.SetGlobalAsync(HotelFrontOfficeSettings.ReservationStatus.CheckInStatusName, input.CheckInStatusName);
        await _settingManager.SetGlobalAsync(HotelFrontOfficeSettings.ReservationStatus.CheckOutStatusName, input.CheckOutStatusName);
        await _settingManager.SetGlobalAsync(HotelFrontOfficeSettings.SettingRoomStatus.OccupiedInStayStatusName, input.OccupiedInStayStatusName);
        await _settingManager.SetGlobalAsync(HotelFrontOfficeSettings.SettingRoomStatus.DirtyStatusName, input.DirtyStatusName);

        return await GetAsync();
    }

    [Authorize(WismaAppPermissions.PolicySettings.Edit)]
    public async Task<bool> UpdateSettingAsync(UpdateSettingDto input)
    {
        try
        {
            // Validate the setting name
            if (!IsValidSettingName(input.Name))
            {
                _logger.LogWarning("Invalid setting name: {SettingName}", input.Name);
                return false;
            }

            // Update the setting
            await _settingManager.SetGlobalAsync(input.Name, input.Value);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating setting {SettingName}: {ErrorMessage}", input.Name, ex.Message);
            return false;
        }
    }

    private static bool IsValidSettingName(string name)
    {
        // Check if the setting name is one of our defined settings
        return name == HotelFrontOfficeSettings.ReservationStatus.CheckInStatusName ||
               name == HotelFrontOfficeSettings.ReservationStatus.CheckOutStatusName ||
               name == HotelFrontOfficeSettings.SettingRoomStatus.OccupiedInStayStatusName ||
               name == HotelFrontOfficeSettings.SettingRoomStatus.DirtyStatusName;
    }
}
