using System;
using DinkToPdf;
using Imip.HotelFrontOffice.Authentication;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.Users;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Volo.Abp.Account;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.AutoMapper;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.TenantManagement;

namespace Imip.HotelFrontOffice;

[DependsOn(
        typeof(HotelFrontOfficeDomainModule),
        typeof(HotelFrontOfficeApplicationContractsModule),
        typeof(AbpPermissionManagementApplicationModule),
        typeof(AbpFeatureManagementApplicationModule),
        typeof(AbpIdentityApplicationModule),
        typeof(AbpAccountApplicationModule),
        typeof(AbpTenantManagementApplicationModule),
        typeof(AbpSettingManagementApplicationModule),
        typeof(PermissionCheckerModule)
        )]
public class HotelFrontOfficeApplicationModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<HotelFrontOfficeApplicationModule>();
        });

        // Register the authorization handler
        context.Services.AddScoped<IAuthorizationHandler, DynamicPolicyAuthorizationHandler>();

        // Register the HttpContextAccessor if it's not already registered
        context.Services.AddHttpContextAccessor();

        // Register the ApplicationConfigurationService
        context.Services.AddScoped<Services.ApplicationConfigurationService>();

        // Register DinkToPdf converter
        var converter = new SynchronizedConverter(new PdfTools());
        context.Services.AddSingleton<DinkToPdf.Contracts.IConverter>(converter);

        // Register document generation services
        context.Services.AddTransient<Documents.Invoice.IInvoiceDocumentService, Documents.Invoice.InvoiceDocumentService>();
        context.Services.AddTransient<Documents.Invoice.WismaInvoiceGenerator>();

        // Register Syncfusion DOCX to PDF conversion service
        context.Services.AddTransient<Documents.ISyncfusionDocxToPdfService, Documents.SyncfusionDocxToPdfService>();

        // Register an HTTP client for Identity Server communication
        context.Services.AddHttpClient("IdentityServer", client =>
        {
            var configuration = context.Services.GetConfiguration();
            client.BaseAddress = new Uri(configuration["AuthServer:Authority"] ?? "https://api-identity-dev.imip.co.id");
        });

        // Register JWT token caching service
        context.Services.AddSingleton<IJwtTokenCacheService, JwtTokenCacheService>();

        // Configure memory cache for performance optimization
        context.Services.AddMemoryCache(options =>
        {
            options.SizeLimit = 10000; // Limit cache size
            options.CompactionPercentage = 0.25; // Compact when 75% full
        });

        // Replace permission checker with cached version for better performance
        context.Services.Replace(ServiceDescriptor.Transient<IPermissionChecker, CachedPermissionChecker>());

        // Register user synchronization service
        context.Services.AddTransient<IUserSynchronizationService, UserSynchronizationService>();
    }
}
