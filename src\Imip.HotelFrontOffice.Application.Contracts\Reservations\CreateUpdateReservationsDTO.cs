﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Imip.HotelFrontOffice.Companies;
using Imip.HotelFrontOffice.MasterStatuses;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Reservations;

public class CreateUpdateReservationsDto : AuditedEntityDto<Guid>, IValidatableObject
{
    public string? ReservationCode { get; set; } = default!;

    public string GroupCode { get; set; } = default!;
    public string BookerName { get; set; } = default!;
    public string BookerIdentityNumber { get; set; } = default!;
    public string BookerPhoneNumber { get; set; } = default!;
    public string BookerEmail { get; set; } = default!;
    public DateTime ArrivalDate { get; set; } = default!;
    public int Days { get; set; } = default!;
    /// <summary>
    /// Legacy attachment field (for backward compatibility)
    /// </summary>
    public string Attachment { get; set; } = default!;

    /// <summary>
    /// Array of attachments for the reservation
    /// </summary>
    public List<ReservationAttachmentDto>? Attachments { get; set; }

    [Required]
    public Guid StatusId { get; set; } = default!;

    [Required]
    public Guid ReservationTypeId { get; set; } = default!;
    public Guid? CompanyId { get; set; } = default!;
    public Guid? PaymentMethodId { get; set; } = default!;
    public Guid? DiningOptionsId { get; set; } = default!;
    public bool IsExtraBed { get; set; } = false;

    /// <summary>
    /// List of reservation details
    /// </summary>
    public List<CreateReservationDetailDto>? ReservationDetails { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        // Get repositories from validation context
        var companyRepository =
            validationContext.GetService(typeof(IRepository<Company, Guid>)) as IRepository<Company, Guid>;
        var statusRepository =
            validationContext.GetService(typeof(IRepository<MasterStatus, Guid>)) as IRepository<MasterStatus, Guid>;
        var reservationTypeRepository =
            validationContext.GetService(typeof(IRepository<ReservationTypes.ReservationType, Guid>)) as IRepository<ReservationTypes.ReservationType, Guid>;

        // Validate CompanyId if provided
        if (CompanyId.HasValue && CompanyId.Value != Guid.Empty && companyRepository != null)
        {
            if (!companyRepository.AnyAsync(c => c.Id == CompanyId.Value).Result)
            {
                yield return new ValidationResult(
                    $"Company with ID {CompanyId.Value} does not exist.",
                    new[] { nameof(CompanyId) }
                );
            }
        }

        // Validate StatusId
        if (StatusId != Guid.Empty && statusRepository != null)
        {
            if (!statusRepository.AnyAsync(s => s.Id == StatusId).Result)
            {
                yield return new ValidationResult(
                    $"Status with ID {StatusId} does not exist.",
                    new[] { nameof(StatusId) }
                );
            }
        }

        // Validate ReservationTypeId
        if (ReservationTypeId != Guid.Empty && reservationTypeRepository != null)
        {
            if (!reservationTypeRepository.AnyAsync(rt => rt.Id == ReservationTypeId).Result)
            {
                yield return new ValidationResult(
                    $"Reservation Type with ID {ReservationTypeId} does not exist.",
                    new[] { nameof(ReservationTypeId) }
                );
            }
        }

        // Validate ArrivalDate is not in the past
        // if (ArrivalDate.Date < DateTime.Now.Date)
        // {
        //     yield return new ValidationResult(
        //         "Arrival date cannot be in the past.",
        //         new[] { nameof(ArrivalDate) }
        //     );
        // }
    }
}