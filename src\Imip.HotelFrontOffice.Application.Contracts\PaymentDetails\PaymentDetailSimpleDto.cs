using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.PaymentDetails;

/// <summary>
/// Simplified PaymentDetail DTO to prevent circular references
/// Contains only essential information without navigation properties that could cause loops
/// </summary>
public class PaymentDetailSimpleDto : AuditedEntityDto<Guid>
{
    public Guid PaymentId { get; set; }

    [EnumDataType(typeof(PaymentSourceType))]
    public PaymentSourceType SourceType { get; set; } = default!;

    public string SourceId { get; set; } = default!;

    public decimal Amount { get; set; } = default!;

    public decimal Qty { get; set; } = default!;
    public decimal UnitPrice { get; set; } = default!;
    public decimal? VatRate { get; set; }
    public decimal? VatAmount { get; set; }

    public Guid? ReservationDetailsId { get; set; }
    public Guid? TaxId { get; set; }

    // Simple properties instead of full navigation objects to prevent circular references
    public string? PaymentCode { get; set; }
    public string? ReservationCode { get; set; }
    public string? GuestName { get; set; }
    public string? RoomNumber { get; set; }
    public string? StatusName { get; set; }
}
