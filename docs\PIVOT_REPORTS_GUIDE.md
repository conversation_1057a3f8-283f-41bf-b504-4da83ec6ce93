# Pivot Reports Implementation Guide

## Overview

The pivot table functionality has been successfully implemented to enhance the existing report system. This feature allows you to transform regular tabular data into pivot table format with dynamic column headers, aggregations, and Excel export capabilities.

## Key Features

### 1. Dynamic Pivot Headers
- Generate column headers dynamically based on data values
- Support for date-based pivots (days, months, years)
- Custom formatting for column headers
- Configurable prefix/suffix for headers

### 2. Pivot Configuration
- **Pivot Column Field**: The field to pivot on (creates dynamic columns)
- **Value Field**: The field to aggregate/sum in the pivot
- **Row Group Fields**: Fields that remain as rows
- **Aggregation Types**: Sum, Count, Average, Min, Max, CountDistinct
- **Totals**: Optional totals row and column

### 3. Excel Export Enhancement
- Dynamic column headers in Excel exports
- Proper cell formatting for pivot data
- Support for both static and dynamic headers
- Compatible with existing Excel header configurations

## Implementation Details

### New Components Added

1. **ReportQueryType.Pivot** - New enum value for pivot reports
2. **ReportPivotConfigDto** - Configuration class for pivot settings
3. **PivotDataProcessor** - Service for transforming data into pivot format
4. **IPivotDataProcessor** - Interface for pivot processing
5. **Enhanced ReportExecutionService** - Supports pivot transformation
6. **New API Endpoints** - For managing pivot configurations

### Database Changes

- Added `PivotConfig` field to `Report` entity (JSON string)
- Updated DTOs to include pivot configuration

## Usage Examples

### 1. Basic Pivot Configuration

```json
{
  "pivotColumnField": "Date",
  "valueField": "Amount",
  "rowGroupFields": ["Category", "Type"],
  "aggregationType": "Sum",
  "includeTotalsRow": true,
  "includeTotalsColumn": true,
  "headerFormat": {
    "dateFormat": "MMM dd",
    "prefix": "",
    "suffix": ""
  },
  "columnSorting": {
    "direction": "Ascending",
    "sortType": "Date"
  }
}
```

### 2. Monthly Sales Pivot

```json
{
  "pivotColumnField": "Month",
  "valueField": "SalesAmount",
  "rowGroupFields": ["Region", "Product"],
  "aggregationType": "Sum",
  "headerFormat": {
    "dateFormat": "MMMM yyyy",
    "customMapping": {
      "1": "January",
      "2": "February",
      "3": "March"
    }
  }
}
```

### 3. Daily Room Occupancy

```json
{
  "pivotColumnField": "Day",
  "valueField": "OccupiedRooms",
  "rowGroupFields": ["RoomType", "Floor"],
  "aggregationType": "Count",
  "headerFormat": {
    "dateFormat": "dd",
    "prefix": "Day ",
    "maxLength": 10
  }
}
```

## API Endpoints

### Pivot Configuration Management

```http
GET /api/app/report/{reportId}/pivot-config
PUT /api/app/report/{reportId}/pivot-config
POST /api/app/report/{reportId}/validate-pivot-config
```

### Testing Pivot Functionality

```http
POST /api/app/report/{reportId}/test-pivot
```

### Report Execution (Enhanced)

```http
POST /api/app/report/preview
POST /api/app/report/export/excel
```

## Configuration Options

### Aggregation Types

- **Sum**: Sum all values in the group
- **Count**: Count number of records
- **Average**: Calculate average value
- **Min**: Find minimum value
- **Max**: Find maximum value
- **CountDistinct**: Count unique values

### Header Formatting

- **DateFormat**: Format for date-based columns (e.g., "MMM dd", "yyyy-MM")
- **Prefix/Suffix**: Add text before/after column headers
- **CustomMapping**: Map original values to display values
- **MaxLength**: Limit header text length

### Column Sorting

- **Direction**: Ascending, Descending, Custom
- **SortType**: Alphabetical, Numerical, Date, Custom
- **CustomOrder**: Predefined column order

## Sample Data Transformation

### Input Data
```
| Category | Type | Date       | Amount |
|----------|------|------------|--------|
| Food     | Meal | 2024-01-01 | 100    |
| Food     | Meal | 2024-01-02 | 150    |
| Beverage | Soft | 2024-01-01 | 50     |
| Beverage | Soft | 2024-01-02 | 75     |
```

### Pivot Output
```
| Category | Type | Jan 01 | Jan 02 | Total |
|----------|------|--------|--------|-------|
| Food     | Meal | 100    | 150    | 250   |
| Beverage | Soft | 50     | 75     | 125   |
| TOTAL    |      | 150    | 225    | 375   |
```

## Best Practices

### 1. Performance Considerations
- Limit pivot columns to avoid excessive memory usage
- Use appropriate aggregation types
- Consider data volume when designing pivots

### 2. Data Preparation
- Ensure pivot column field has reasonable number of unique values
- Validate that value field contains numeric data for aggregations
- Use proper data types for sorting

### 3. User Experience
- Provide meaningful column headers
- Include totals for better insights
- Use appropriate date formats for time-based pivots

## Error Handling

The system includes comprehensive validation:
- Field existence validation
- Data type compatibility checks
- Performance warnings for large datasets
- Configuration validation before execution

## Integration with Existing Reports

The pivot functionality is fully backward compatible:
- Existing reports continue to work unchanged
- Pivot features are opt-in via configuration
- Excel exports maintain existing functionality
- All existing permissions and security apply

## Future Enhancements

Potential improvements for future versions:
- Multiple value fields in single pivot
- Calculated fields and formulas
- Interactive pivot configuration UI
- Cached pivot results for performance
- Advanced filtering within pivot data
