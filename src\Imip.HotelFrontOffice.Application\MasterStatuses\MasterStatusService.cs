using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.MasterStatuses;

[Route("api/master/status-service")]
[Authorize(WismaAppPermissions.PolicyMasterStatus.Default)]
public class MasterStatusService : ApplicationService, IMasterStatusService
{
    private readonly IMasterStatusRepository _masterStatusRepository;
    private readonly IRepository<MasterStatuses.MasterStatus, Guid> _repository;

    public MasterStatusService(
        IMasterStatusRepository masterStatusRepository,
        IRepository<MasterStatuses.MasterStatus, Guid> repository)
    {
        _masterStatusRepository = masterStatusRepository;
        _repository = repository;
    }

    [HttpGet("by-doc-type/{docType}")]
    [Authorize(WismaAppPermissions.PolicyMasterStatus.View)]
    public async Task<List<MasterStatusDto>> GetByDocTypeAsync(string docType)
    {
        var entities = await _masterStatusRepository.GetByDocTypeAsync(docType);
        return ObjectMapper.Map<List<MasterStatuses.MasterStatus>, List<MasterStatusDto>>(entities);
    }

    [HttpGet("by-id-and-doc-type/{id}/{docType}")]
    [Authorize(WismaAppPermissions.PolicyMasterStatus.View)]
    public async Task<MasterStatusDto?> GetByIdAndDocTypeAsync(Guid id, string docType)
    {
        var entity = await _masterStatusRepository.GetByIdAndDocTypeAsync(id, docType);
        return entity != null ? ObjectMapper.Map<MasterStatuses.MasterStatus, MasterStatusDto>(entity) : null;
    }

    [HttpGet("paged-by-doc-type/{docType}")]
    [Authorize(WismaAppPermissions.PolicyMasterStatus.View)]
    public async Task<PagedResultDto<MasterStatusDto>> GetPagedListByDocTypeAsync(PagedAndSortedResultRequestDto input,
        string docType)
    {
        var queryable = await _repository.GetQueryableAsync();
        var filteredQuery = queryable.Where(x => x.DocType == docType);

        var totalCount = await AsyncExecuter.CountAsync(filteredQuery);

        var sortedQuery = filteredQuery;
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            // Use Dynamic LINQ for sorting
            sortedQuery = filteredQuery.OrderBy(input.Sorting);
        }
        else
        {
            sortedQuery = filteredQuery.OrderBy(x => x.Name);
        }

        var pagedQuery = sortedQuery
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        var entities = await AsyncExecuter.ToListAsync(pagedQuery);
        var dtos = ObjectMapper.Map<List<MasterStatuses.MasterStatus>, List<MasterStatusDto>>(entities);

        return new PagedResultDto<MasterStatusDto>(totalCount, dtos);
    }
}