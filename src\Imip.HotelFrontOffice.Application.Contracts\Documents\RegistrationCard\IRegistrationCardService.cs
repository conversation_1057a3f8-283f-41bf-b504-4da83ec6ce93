﻿using Imip.HotelFrontOffice.Attachments;
using System.Threading.Tasks;

namespace Imip.HotelFrontOffice.Documents.RegistrationCard;
public interface IRegistrationCardService
{
    /// <summary>
    /// Generates a document and saves it as an attachment
    /// </summary>
    /// <param name="input">The invoice generation input</param>
    /// <returns>The file upload result with stream URL</returns>
    Task<FileUploadResultDto> GenerateDocumentAsAttachmentAsync(DocumentGenerationDto input);
}
