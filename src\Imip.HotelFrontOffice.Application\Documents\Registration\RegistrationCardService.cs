﻿using DocumentFormat.OpenXml.Packaging;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.Documents.Invoice;
using Imip.HotelFrontOffice.Documents.RegistrationCard;
using Imip.HotelFrontOffice.ReservationDetails;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.BlobStoring;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Documents.Registration;
public class RegistrationCardService : ApplicationService, IRegistrationCardService
{
    private readonly IRepository<ReservationDetail, Guid> _reservationDetailRepository;
    private readonly IRepository<DocumentTemplate, Guid> _documentTemplateRepository;
    private readonly IAttachmentRepository _attachmentRepository;
    private readonly IDocumentTemplateRepository _documentTemplateCustomRepository;
    private readonly IAttachmentAppService _attachmentAppService;
    private readonly IBlobContainer _blobContainer;
    private readonly IConfiguration _configuration;
    private readonly ISyncfusionDocxToPdfService _syncfusionDocxToPdfService;
    private readonly WismaInvoiceGenerator _wismaInvoiceGenerator;

    public RegistrationCardService(
        IRepository<ReservationDetail, Guid> paymentRepository,
        IRepository<DocumentTemplate, Guid> documentTemplateRepository,
        IAttachmentRepository attachmentRepository,
        IDocumentTemplateRepository documentTemplateCustomRepository,
        IBlobContainer blobContainer,
        IConfiguration configuration,
        IAttachmentAppService attachmentAppService,
        WismaInvoiceGenerator wismaInvoiceGenerator,
        ISyncfusionDocxToPdfService syncfusionDocxToPdfService)
    {
        _reservationDetailRepository = paymentRepository;
        _documentTemplateRepository = documentTemplateRepository;
        _attachmentRepository = attachmentRepository;
        _documentTemplateCustomRepository = documentTemplateCustomRepository;
        _blobContainer = blobContainer;
        _configuration = configuration;
        _attachmentAppService = attachmentAppService;
        _wismaInvoiceGenerator = wismaInvoiceGenerator;
        _syncfusionDocxToPdfService = syncfusionDocxToPdfService;
    }

    public async Task<FileUploadResultDto> GenerateDocumentAsAttachmentAsync(DocumentGenerationDto input)
    {
        // Generate the invoice file
        var fileDto = await GenerateDocumentAsync(input);

        // Get the payment to use as reference
        var reservationDetail = await _reservationDetailRepository.GetAsync(input.DocumentId);

        // Create file upload DTO
        var fileUploadDto = new FileUploadDto
        {
            Description = $"Registration Card for guest {reservationDetail.Guest.Fullname} in room {reservationDetail.Room.RoomNumber}",
            ReferenceId = reservationDetail.Id,
            ReferenceType = "ReservationDetail"
        };

        // Upload the file as an attachment
        var result = await _attachmentAppService.UploadFileAsync(
            fileUploadDto,
            fileDto.FileName,
            fileDto.ContentType,
            fileDto.Content);

        return result;
    }

    /// <summary>
    /// Gets the invoice template data for a payment
    /// </summary>
    private async Task<RegistrationCardDataDto> GetTemplateDataAsync(Guid reservationDetailId)
    {
        // Get the payment with related entities
        var reservationDetailQuery = await _reservationDetailRepository.WithDetailsAsync(
            p => p.Reservation!,
            p => p.PaymentDetails!,
            p => p.Status!,
            p => p.Room!,
            p => p.Guest!
        );

        var reservationDetail = reservationDetailQuery.FirstOrDefault(p => p.Id == reservationDetailId)
            ?? throw new UserFriendlyException(L["RervationDetailNotFound"]);


        // Create the invoice data
        var registrationCardData = new RegistrationCardDataDto
        {
            GuestName = reservationDetail.Guest.Fullname,
            Gender = reservationDetail.Guest.Gender,
            DateOfBirthday = reservationDetail.Guest.DateOfBirthday,
            IdentityNumber = reservationDetail.Guest.IdentityNumber,
            Nationality = reservationDetail.Guest.Nationality,
            Address = reservationDetail.Guest.Address,
            PhoneNumber = reservationDetail.Guest.PhoneNumber,
            MarketCode = reservationDetail.MarketCode,
            CheckInDate = reservationDetail.CheckInDate,
            CheckOutDate = reservationDetail.CheckOutDate,
            RoomNumber = reservationDetail.Room.RoomNumber,
            Price = reservationDetail.Room.Price
        };

        return registrationCardData;
    }

    /// <summary>
    /// Generates a Wisma invoice document for a payment
    /// </summary>
    private async Task<FileDto> GenerateDocumentAsync(DocumentGenerationDto input)
    {
        // Get the payment with related entities
        var payment = await _reservationDetailRepository.GetAsync(input.DocumentId);
        if (payment == null)
        {
            throw new UserFriendlyException(L["ReservationDetailNotFound"]);
        }

        // Get the template
        DocumentTemplate? template;
        if (input.TemplateId.HasValue)
        {
            template = await _documentTemplateRepository.GetAsync(input.TemplateId.Value);
        }
        else
        {
            template = await _documentTemplateCustomRepository.GetDefaultTemplateAsync(DocumentType.RegistrationCard);
            if (template == null)
            {
                throw new UserFriendlyException(L["NoDefaultTemplateFound", DocumentType.RegistrationCard]);
            }
        }

        // Get the attachment (DOCX template)
        var attachment = await _attachmentRepository.GetAsync(template.AttachmentId);
        if (attachment == null)
        {
            throw new UserFriendlyException(L["TemplateAttachmentNotFound"]);
        }

        // Get the DOCX file from blob storage
        if (attachment.BlobName == null)
        {
            throw new UserFriendlyException(L["TemplateBlobNameNotFound"]);
        }

        var docxStream = await _blobContainer.GetAsync(attachment.BlobName);
        if (docxStream == null)
        {
            throw new UserFriendlyException(L["TemplateFileNotFound"]);
        }

        // Read the DOCX file into memory
        using var memoryStream = new MemoryStream();
        await docxStream.CopyToAsync(memoryStream);
        memoryStream.Position = 0;

        // Get the invoice data
        var registrationCardData = await GetTemplateDataAsync(input.DocumentId);

        // Process the template using the enhanced Wisma invoice generator V2
        using (var wordDocument = WordprocessingDocument.Open(memoryStream, true))
        {
            var mainPart = wordDocument.MainDocumentPart;

            var document = mainPart.Document;

            // Create a dictionary of placeholders and their values
            var placeholders = new Dictionary<string, string>
            {
                { "{{GUEST_NAME}}", registrationCardData.GuestName },
                { "{{GENDER}}", registrationCardData.Gender },
                { "{{DOB}}", registrationCardData.DateOfBirthday?.ToString("dddd dd MMMM yyyy") ?? string.Empty },
                { "{{IDENTITY_NO}}", registrationCardData.IdentityNumber },
                { "{{NATIONALITY}}", registrationCardData.Nationality },
                { "{{ADDRESS}}", registrationCardData.Address },
                { "{{MARKET_CODE}}", registrationCardData.MarketCode.ToString() },
                { "{{ROOM_NO}}", registrationCardData.RoomNumber },
                { "{{ARRIVAL_DATE}}", registrationCardData.CheckInDate?.ToString("dd/MM/yyyy") ?? string.Empty },
                { "{{DEPARTURE_DATE}}", registrationCardData.CheckOutDate?.ToString("dd/MM/yyyy") ?? string.Empty },
                { "{{ROOM_RATE}}", registrationCardData.Price.HasValue ? Math.Floor(registrationCardData.Price.Value).ToString("#,##0") : "0" },
                { "{{CONTACT_NO}}", registrationCardData.PhoneNumber },
            };

            // With this corrected line:
            WismaInvoiceGenerator.ReplaceTextInDocument(mainPart.Document, placeholders);
        }

        // Generate the filename
        string guestSlug = Regex.Replace(registrationCardData.GuestName, @"[^a-zA-Z0-9]+", "_");
        var filename = input.CustomFilename ?? $"Registration_Card_{guestSlug}_{registrationCardData.RoomNumber}_{DateTime.Now:yyyyMMdd_HHmmss}";

        // Return the file
        if (input.GeneratePdf)
        {
            try
            {
                // Convert to PDF using our Syncfusion-based conversion service
                return await _syncfusionDocxToPdfService.ConvertDocxToPdfAsync(memoryStream.ToArray(), filename);
            }
            catch (OutOfMemoryException ex)
            {
                Logger.LogError(ex, "Out of memory during PDF conversion for Wisma invoice {Filename}. Returning DOCX instead.", filename);
                return new FileDto(
                    $"{filename}.docx",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    memoryStream.ToArray()
                );
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "PDF conversion failed for Wisma invoice {Filename}. Returning DOCX instead.", filename);
                return new FileDto(
                    $"{filename}.docx",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    memoryStream.ToArray()
                );
            }
        }
        else
        {
            return new FileDto(
                $"{filename}.docx",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                memoryStream.ToArray()
            );
        }
    }
}
