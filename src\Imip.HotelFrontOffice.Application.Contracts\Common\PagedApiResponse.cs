using System;

namespace Imip.HotelFrontOffice.Common;

/// <summary>
/// Standard API response for paged data
/// </summary>
/// <typeparam name="T">Type of the data</typeparam>
public class PagedApiResponse<T>
{
    /// <summary>
    /// Metadata about the paging
    /// </summary>
    public MetaData Meta { get; set; } = null!;

    /// <summary>
    /// Whether the operation was successful
    /// </summary>
    public bool Success { get; set; } = true;

    /// <summary>
    /// Message about the operation
    /// </summary>
    public string Message { get; set; } = "Operation successful";

    /// <summary>
    /// The data returned
    /// </summary>
    public T Data { get; set; } = default!;

    /// <summary>
    /// Create a successful response with data and metadata
    /// </summary>
    public static PagedApiResponse<T> FromResult(T data, MetaData meta, string message = "Operation successful")
    {
        return new PagedApiResponse<T>
        {
            Success = true,
            Message = message,
            Data = data,
            Meta = meta
        };
    }

    /// <summary>
    /// Create a failed response with an error message
    /// </summary>
    public static PagedApiResponse<T> FromError(string errorMessage)
    {
        return new PagedApiResponse<T>
        {
            Success = false,
            Message = errorMessage,
            Meta = new MetaData()
        };
    }

    /// <summary>
    /// Create a failed response with an exception
    /// </summary>
    public static PagedApiResponse<T> FromException(Exception ex)
    {
        return new PagedApiResponse<T>
        {
            Success = false,
            Message = ex.Message,
            Meta = new MetaData()
        };
    }
}
