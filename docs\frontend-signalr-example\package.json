{"name": "hotel-room-status-dashboard", "version": "1.0.0", "description": "Real-time hotel room status dashboard using SignalR", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@microsoft/signalr": "^8.0.0", "next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "postcss": "^8.0.0", "tailwindcss": "^3.0.0", "typescript": "^5.0.0"}, "keywords": ["signalr", "real-time", "hotel", "room-status", "dashboard", "nextjs", "react", "typescript"], "author": "Your Name", "license": "MIT"}