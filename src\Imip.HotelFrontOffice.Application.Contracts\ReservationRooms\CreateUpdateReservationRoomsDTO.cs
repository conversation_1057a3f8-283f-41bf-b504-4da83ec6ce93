﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.ReservationRooms;

public class CreateUpdateReservationRoomsDto
{
    // Id property is required for bulk updates
    public Guid Id { get; set; }

    [Required]
    public Guid ReservationDetailsId { get; set; } = default!;

    [Required]
    public Guid ServiceId { get; set; } = default!;
    public Guid? PaymentStatusId { get; set; } = default!; // Optional property for payment status ID
    [Required]
    [Range(0, double.MaxValue, ErrorMessage = "Total price must be greater than or equal to 0")]
    public decimal TotalPrice { get; set; } = default!;

    [Required]
    [Range(1, int.MaxValue, ErrorMessage = "Quantity must be at least 1")]
    public int Quantity { get; set; } = default!;

    [Required]
    public DateTime TransactionDate { get; set; } = default!;

}