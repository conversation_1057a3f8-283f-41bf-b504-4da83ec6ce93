# PDF Size Optimization Fix for Kubernetes Environment

## Problem Description

PDF files generated in the Kubernetes development environment were unexpectedly large (25MB) compared to normal-sized files generated locally. This issue was caused by font handling and rendering differences between the local Windows environment and the Linux Kubernetes containers.

## Root Cause Analysis

The large PDF files in Kubernetes were caused by:

1. **CJK Font Embedding**: The main culprit was `EastAsia = "Noto Sans CJK SC"` in TableGenerator.cs, which embeds a massive Chinese font file (~20MB+)
2. **Font Embedding Issues**: Syncfusion DocIORenderer was embedding complete fonts instead of font subsets in the Linux container environment
3. **Missing Font Optimization**: The container environment lacked proper font optimization settings
4. **Different Rendering Behavior**: SkiaSharp font rendering behaves differently between Windows and Linux environments
5. **Lack of PDF Compression**: No explicit PDF compression settings were configured

**Key Discovery**: The `Noto Sans CJK SC` font specification in the table generation code was the primary cause of the 25MB file sizes.

## Implemented Solution

### 1. **Critical Font Fix in TableGenerator.cs** ⭐ **PRIMARY FIX**

**File**: `src/Imip.HotelFrontOffice.Application/Documents/Invoice/TableGenerator.cs`

**The Problem**:
```csharp
// BEFORE - This was causing 25MB PDFs
EastAsia = "Noto Sans CJK SC" // Embeds massive Chinese font file
```

**The Solution**:
```csharp
// AFTER - Removed CJK font specification
runProperties.AppendChild(new RunFonts
{
    Ascii = "Arial", // Use Arial instead of Calibri for smaller size
    HighAnsi = "Arial",
    ComplexScript = "Arial"
    // Removed EastAsia font specification to prevent large font embedding
    // The system will use default fonts for Chinese characters
});
```

**Impact**: This single change should reduce PDF file sizes from 25MB to normal sizes (< 1MB).

### 2. **Enhanced PDF Optimization in SyncfusionDocxToPdfService**

**File**: `src/Imip.HotelFrontOffice.Application/Documents/SyncfusionDocxToPdfService.cs`

**Key Changes**:
- Added `ConfigurePdfOptimization()` method to configure DocIORenderer settings
- Added `OptimizePdfDocument()` method to apply PDF-level optimizations
- Disabled font embedding (`EmbedFonts = false`) to prevent large font files
- Enabled image optimization (`OptimizeIdenticalImages = true`)
- Set PDF compression to maximum (`PdfCompressionLevel.Best`)
- Enhanced logging with detailed file size information and warnings

**Optimization Settings**:
```csharp
// Font optimization
docIORenderer.Settings.EmbedFonts = false; // Don't embed fonts to reduce size

// Image optimization
docIORenderer.Settings.OptimizeIdenticalImages = true;

// PDF compression
pdfDocument.Compression = PdfCompressionLevel.Best;
pdfDocument.FileStructure.Version = PdfVersion.Version1_7;
```

### 2. **Configurable PDF Optimization Settings**

**Files**:
- `src/Imip.HotelFrontOffice.Web/appsettings.json`
- `k8s/dev/configmap.yaml`
- `k8s/prod/configmap.yaml`

**New Configuration Section**:
```json
"PdfOptimization": {
  "EmbedFonts": false,
  "OptimizeIdenticalImages": true,
  "CompressionLevel": "Best",
  "MaxFileSizeWarningMB": 5.0
}
```

### 3. **Enhanced Monitoring and Logging**

**Features**:
- Detailed file size logging (DOCX vs PDF size, compression ratio)
- Configurable file size warning threshold
- Memory usage monitoring during conversion
- Environment-specific optimization settings

**Sample Log Output**:
```
Successfully converted Invoice_12345 to PDF. DOCX size: 45.23 KB, PDF size: 156.78 KB (0.15 MB), Compression ratio: 0.29x
```

## Configuration Details

### Development Environment
- `EmbedFonts`: false (reduces file size)
- `OptimizeIdenticalImages`: true (optimizes repeated images)
- `MaxFileSizeWarningMB`: 5.0 (warns if PDF > 5MB)

### Production Environment
- Same settings as development for consistency
- Higher concurrent conversion limit (4 vs 2)

## Expected Results

### Before Fix
- PDF files: ~25MB (unusually large)
- Poor compression ratio
- Font embedding causing bloat

### After Fix
- PDF files: Normal size (typically < 1MB for invoices)
- Improved compression ratio
- No unnecessary font embedding
- Better performance in Kubernetes environment

## Monitoring and Verification

### 1. **Check Application Logs**
Look for log entries showing file size information:
```bash
kubectl logs -f deployment/imip-wisma-web -n imip-wisma-dev-new | grep "Successfully converted"
```

### 2. **Monitor for Size Warnings**
Watch for warnings about large PDF files:
```bash
kubectl logs -f deployment/imip-wisma-web -n imip-wisma-dev-new | grep "unusually large"
```

### 3. **Test PDF Generation**
1. Generate an invoice through the API
2. Check the response for file size information
3. Download and verify the PDF file size
4. Compare with previous generations

### 4. **Configuration Verification**
Verify the configuration is loaded correctly:
```bash
kubectl exec -it deployment/imip-wisma-web -n imip-wisma-dev-new -- printenv | grep PdfOptimization
```

## Troubleshooting

### If PDFs are Still Large

1. **Check Configuration Loading**:
   - Verify environment variables are set correctly
   - Check application logs for configuration warnings

2. **Font Issues**:
   - Ensure font packages are installed in the container
   - Check SkiaSharp library configuration
   - Verify font cache is properly built

3. **Image Issues**:
   - Check if documents contain large embedded images
   - Verify image optimization is working

### Common Issues

1. **Configuration Not Applied**:
   - Restart the deployment after configuration changes
   - Check for typos in environment variable names

2. **Font Fallback Problems**:
   - Ensure proper fonts are available in the container
   - Check font configuration in Dockerfile

## Files Modified

### Application Code
1. `src/Imip.HotelFrontOffice.Application/Documents/SyncfusionDocxToPdfService.cs`
   - Added PDF optimization methods
   - Enhanced logging and monitoring
   - Configurable settings support

### Configuration Files
2. `src/Imip.HotelFrontOffice.Web/appsettings.json`
   - Added PdfOptimization configuration section

3. `k8s/dev/configmap.yaml`
   - Added PDF optimization environment variables

4. `k8s/prod/configmap.yaml`
   - Added PDF optimization environment variables

## Testing Recommendations

1. **Generate Test Invoices**:
   - Create invoices with different content types
   - Test with various payment details
   - Verify file sizes are reasonable

2. **Performance Testing**:
   - Monitor conversion times
   - Check memory usage during conversion
   - Test concurrent conversions

3. **Quality Verification**:
   - Ensure PDF quality is acceptable
   - Verify all content is properly rendered
   - Test PDF compatibility with viewers

## Future Improvements

1. **Dynamic Quality Settings**: Allow per-request quality settings
2. **Advanced Image Compression**: Implement more sophisticated image optimization
3. **Font Subsetting**: Implement proper font subsetting for required fonts
4. **Caching**: Cache optimized fonts and settings for better performance
