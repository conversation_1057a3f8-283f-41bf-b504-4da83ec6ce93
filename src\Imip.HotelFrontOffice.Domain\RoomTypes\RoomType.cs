﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.Rooms;
using Volo.Abp.Data;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.RoomTypes;

public class RoomType : FullAuditedAggregateRoot<Guid>
{
    public string Name { get; set; } = default!;
    public string Alias { get; set; } = default!;
    public Guid? StatusId { get; set; }

    public virtual ICollection<Room> Rooms { get; set; }
    public virtual MasterStatus? Status { get; set; }

    protected RoomType()
    {
        Rooms = new HashSet<Room>();
    }

    public RoomType(Guid id, string name, Guid? statusId, string alias) : this()
    {
        Id = id;
        Name = name;
        Alias = alias;
        StatusId = statusId;
    }

    // Helper methods for ExtraProperties

    /// <summary>
    /// Sets a value in the ExtraProperties dictionary
    /// </summary>
    public void SetExtraProperty<T>(string key, T value)
    {
        this.SetProperty(key, value);
    }

    /// <summary>
    /// Gets a value from the ExtraProperties dictionary
    /// </summary>
    public T GetExtraProperty<T>(string key)
    {
        return this.GetProperty<T>(key);
    }

    /// <summary>
    /// Removes a property from the ExtraProperties dictionary
    /// </summary>
    public void RemoveExtraProperty(string key)
    {
        this.RemoveProperty(key);
    }
}