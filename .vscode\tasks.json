{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Imip.HotelFrontOffice.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary;ForceNoAlign"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/Imip.HotelFrontOffice.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary;ForceNoAlign"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/Imip.HotelFrontOffice.sln"], "problemMatcher": "$msCompile"}, {"label": "dotrush: Build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/Imip.HotelFrontOffice.Web/Imip.HotelFrontOffice.Web.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}, {"label": "dotrush: Build Migrator", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/Imip.HotelFrontOffice.Web/Imip.DbMigrator.Web.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}]}