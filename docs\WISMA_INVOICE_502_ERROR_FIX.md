# Fix for Wisma Invoice Generation 502 Bad Gateway Error in Kubernetes Dev Environment

## Problem Analysis

The 502 Bad Gateway error and pod shutdowns when generating Wisma invoices in the Kubernetes dev environment were caused by:

1. **Memory Exhaustion**: The dev environment had insufficient memory limits (256Mi request) for Syncfusion document conversion
2. **Resource Competition**: Multiple concurrent PDF conversions consuming excessive memory
3. **Lack of Error Handling**: No graceful degradation when PDF conversion fails
4. **Missing Resource Monitoring**: No visibility into memory usage during document processing

## Root Cause

The Syncfusion DocIO library used for DOCX to PDF conversion is memory-intensive, especially when:
- Processing complex Word documents with tables and formatting
- Multiple conversions running simultaneously
- Running in constrained Kubernetes environments

## Implemented Fixes

### 1. **Increased Memory Limits in Dev Environment**

**File**: `k8s/dev/web-deployment.yaml`

```yaml
resources:
  requests:
    memory: "768Mi"
    cpu: "500m"
  limits:
    memory: "2Gi"
    cpu: "1000m"
```

**Changes**:
- Increased memory request from 256Mi to 768Mi
- Increased memory limit from 512Mi to 2Gi
- Added proper CPU limits to prevent throttling

### 2. **Added Concurrency Control**

**File**: `src/Imip.HotelFrontOffice.Application/Documents/SyncfusionDocxToPdfService.cs`

**Changes**:
- Added semaphore to limit concurrent conversions (max 2)
- Implemented memory monitoring before/after conversions
- Added explicit garbage collection after each conversion
- Enhanced logging for debugging

### 3. **Implemented Circuit Breaker Pattern**

**File**: `src/Imip.HotelFrontOffice.Application/Documents/Invoice/InvoiceDocumentService.cs`

**Changes**:
- Added graceful fallback to DOCX when PDF conversion fails
- Specific handling for OutOfMemoryException
- Enhanced error logging

### 4. **Added Health Monitoring**

**File**: `src/Imip.HotelFrontOffice.Web/HealthChecks/DocumentConversionHealthCheck.cs`

**Features**:
- Memory usage monitoring
- Warning thresholds (1GB) and critical thresholds (1.5GB)
- Garbage collection statistics
- Health status reporting

### 5. **Enhanced Environment Configuration**

**File**: `k8s/dev/web-deployment.yaml`

**Added Environment Variables**:
```yaml
- name: SYNCFUSION_DEBUG
  value: "true"
- name: SKIASHARP_DEBUG
  value: "true"
- name: DOTNET_gcServer
  value: "1"
- name: DOTNET_gcConcurrent
  value: "1"
```

## Deployment Instructions

### Step 1: Apply Kubernetes Changes

#### For Development Environment:
```bash
# Navigate to the project root
cd /path/to/imip.hotelfrontoffice

# Apply the updated configuration and deployment
kubectl apply -f k8s/dev/configmap.yaml
kubectl apply -f k8s/dev/skiasharp-config.yaml
kubectl apply -f k8s/dev/web-deployment.yaml

# Check pod status
kubectl get pods -n imip-wisma-dev-new

# Monitor pod startup
kubectl logs -f deployment/imip-wisma-web -n imip-wisma-dev-new
```

#### For Production Environment:
```bash
# Apply the updated configuration and deployment
kubectl apply -f k8s/prod/configmap.yaml
kubectl apply -f k8s/prod/skiasharp-config.yaml
kubectl apply -f k8s/prod/web-deployment.yaml

# Check pod status
kubectl get pods -n imip-wisma-prod

# Monitor pod startup
kubectl logs -f deployment/imip-wisma-web -n imip-wisma-prod
```

### Step 2: Rebuild and Deploy Application

```bash
# Trigger GitLab CI/CD pipeline or manual deployment
# The application will be rebuilt with the new code changes
```

### Step 3: Monitor Health Checks

```bash
# Check health endpoint
curl http://your-dev-server/health

# Check specific document conversion health
curl http://your-dev-server/api/health/kubernetes
```

## Monitoring and Verification

### 1. **Memory Usage Monitoring**

Monitor pod memory usage:
```bash
kubectl top pods -n imip-wisma-dev-new
```

### 2. **Application Logs**

Check for conversion logs:
```bash
kubectl logs -f deployment/imip-wisma-web -n imip-wisma-dev-new | grep "PDF conversion"
```

### 3. **Health Check Endpoint**

The health check endpoint provides memory statistics:
- `/health` - Overall application health
- Memory usage in MB
- GC collection counts
- Health status (Healthy/Degraded/Unhealthy)

## Expected Results

After applying these fixes:

1. **No More 502 Errors**: PDF conversion should complete successfully
2. **No Pod Shutdowns**: Memory usage stays within limits
3. **Graceful Degradation**: If PDF conversion fails, DOCX is returned instead
4. **Better Monitoring**: Health checks provide visibility into memory usage
5. **Improved Performance**: Controlled concurrency prevents resource exhaustion

## Troubleshooting

### If Issues Persist:

1. **Check Memory Limits**:
   ```bash
   kubectl describe pod <pod-name> -n imip-wisma-dev-new
   ```

2. **Monitor Resource Usage**:
   ```bash
   kubectl top pods -n imip-wisma-dev-new --containers
   ```

3. **Check Application Logs**:
   ```bash
   kubectl logs deployment/imip-wisma-web -n imip-wisma-dev-new --tail=100
   ```

4. **Test Health Endpoint**:
   ```bash
   curl -v http://your-dev-server/health
   ```

### Emergency Rollback

If the changes cause issues:
```bash
# Rollback to previous deployment
kubectl rollout undo deployment/imip-wisma-web -n imip-wisma-dev-new

# Check rollback status
kubectl rollout status deployment/imip-wisma-web -n imip-wisma-dev-new
```

## Performance Recommendations

1. **Consider PDF Generation Alternatives**: For high-volume scenarios, consider using external services like Gotenberg
2. **Implement Caching**: Cache generated PDFs to avoid regeneration
3. **Async Processing**: Move PDF generation to background jobs for large documents
4. **Resource Scaling**: Consider horizontal pod autoscaling based on memory usage

## Production Environment Updates

The production environment has been updated with enhanced resource limits and configuration:

### **Resource Limits (Production)**
```yaml
resources:
  requests:
    memory: "1Gi"      # Increased from 768Mi
    cpu: "500m"
  limits:
    memory: "2.5Gi"    # Increased from 1.5Gi
    cpu: "1500m"       # Increased from 1000m
```

### **Concurrency Configuration**
- **Development**: Max 2 concurrent PDF conversions
- **Production**: Max 4 concurrent PDF conversions (higher throughput)

### **Environment Variables Added**
Both dev and production now include:
- `DOTNET_gcServer=1` - Server GC for better memory management
- `DOTNET_gcConcurrent=1` - Concurrent garbage collection
- `SKIASHARP_DEBUG=true` - Enhanced debugging for SkiaSharp issues

## Files Modified

### **Kubernetes Configuration**
1. `k8s/dev/web-deployment.yaml` - Increased memory limits and added environment variables
2. `k8s/prod/web-deployment.yaml` - Enhanced resource limits for production workload
3. `k8s/dev/configmap.yaml` - Added DocumentConversion:MaxConcurrentConversions=2
4. `k8s/prod/configmap.yaml` - Added DocumentConversion:MaxConcurrentConversions=4
5. `k8s/dev/skiasharp-config.yaml` - Added GC configuration
6. `k8s/prod/skiasharp-config.yaml` - Added GC configuration

### **Application Code**
7. `src/Imip.HotelFrontOffice.Application/Documents/SyncfusionDocxToPdfService.cs` - Added configurable concurrency control and memory management
8. `src/Imip.HotelFrontOffice.Application/Documents/Invoice/InvoiceDocumentService.cs` - Added circuit breaker pattern
9. `src/Imip.HotelFrontOffice.Web/HealthChecks/DocumentConversionHealthCheck.cs` - New health check
10. `src/Imip.HotelFrontOffice.Web/HotelFrontOfficeWebModule.cs` - Registered health check
11. `src/Imip.HotelFrontOffice.Web/appsettings.json` - Added DocumentConversion configuration
