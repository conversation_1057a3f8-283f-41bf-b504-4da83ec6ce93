using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Reports;

[RemoteService(false)]
public class ReportExecutionService : ApplicationService, IReportExecutionService
{
    private readonly IRepository<Report, Guid> _reportRepository;
    private readonly IDbContextProvider<HotelFrontOfficeDbContext> _dbContextProvider;
    private readonly IPivotDataProcessor _pivotDataProcessor;
    private readonly ConditionalFormattingService _conditionalFormattingService;

    public ReportExecutionService(
        IRepository<Report, Guid> reportRepository,
        IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider,
        IPivotDataProcessor pivotDataProcessor,
        ConditionalFormattingService conditionalFormattingService)
    {
        _reportRepository = reportRepository;
        _dbContextProvider = dbContextProvider;
        _pivotDataProcessor = pivotDataProcessor;
        _conditionalFormattingService = conditionalFormattingService;

        // Set EPPlus license context for non-commercial use
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    public async Task<ReportPreviewDto> ExecuteReportAsync(Guid reportId, Dictionary<string, object> parameters)
    {
        var report = await _reportRepository.GetAsync(reportId);

        if (!report.IsActive)
            throw new BusinessException("Report is not active");

        // Extract parameters and excel header config from the report object before opening DataReader
        var reportParameters = ExtractReportParameters(report);
        var excelHeaderConfig = ExtractReportExcelHeaderConfig(report);

        var dbContext = await _dbContextProvider.GetDbContextAsync();
        var connection = dbContext.Database.GetDbConnection();

        var processedQuery = ProcessQueryParameters(report.Query, parameters);

        // Check if connection is already open, if not open it
        bool wasConnectionClosed = connection.State == ConnectionState.Closed;

        try
        {
            if (wasConnectionClosed)
            {
                await connection.OpenAsync();
            }

            using var command = connection.CreateCommand();
            command.CommandText = processedQuery;
            command.CommandTimeout = 300; // 5 minutes timeout

            // Assign the current transaction if one exists
            var currentTransaction = dbContext.Database.CurrentTransaction;
            if (currentTransaction != null)
            {
                command.Transaction = currentTransaction.GetDbTransaction();
            }

            // Set command type based on report type
            switch (report.QueryType)
            {
                case ReportQueryType.StoredProcedure:
                    command.CommandType = CommandType.StoredProcedure;
                    AddParametersToCommand(command, parameters);
                    break;
                case ReportQueryType.RawQuery:
                case ReportQueryType.View:
                case ReportQueryType.Pivot:
                    command.CommandType = CommandType.Text;
                    break;
            }

            using var reader = await command.ExecuteReaderAsync();

            var result = new ReportPreviewDto
            {
                ReportName = report.Name,
                ExecutedAt = DateTime.UtcNow,
                Parameters = reportParameters,
                ExcelHeaderConfig = excelHeaderConfig
            };

            // Get column names
            for (int i = 0; i < reader.FieldCount; i++)
            {
                result.Columns.Add(reader.GetName(i));
            }

            // Read data
            while (await reader.ReadAsync())
            {
                var row = new Dictionary<string, object>();
                for (int i = 0; i < reader.FieldCount; i++)
                {
                    var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                    row[reader.GetName(i)] = value;
                }
                result.Data.Add(row);
            }

            result.TotalRows = result.Data.Count;

            // Apply pivot transformation if this is a pivot report
            if (report.QueryType == ReportQueryType.Pivot && !string.IsNullOrEmpty(report.PivotConfig))
            {
                try
                {
                    var pivotConfig = JsonSerializer.Deserialize<ReportPivotConfigDto>(report.PivotConfig);
                    if (pivotConfig != null)
                    {
                        var pivotResult = await _pivotDataProcessor.TransformToPivotAsync(result, pivotConfig);
                        return pivotResult.PivotData;
                    }
                }
                catch (JsonException)
                {
                    // Log error and continue with non-pivot data
                    // Could add logging here if needed
                }
            }

            return result;
        }
        finally
        {
            // Only close the connection if we opened it
            if (wasConnectionClosed && connection.State == ConnectionState.Open)
            {
                await connection.CloseAsync();
            }
        }
    }

    public async Task<byte[]> ExportReportToCsvAsync(Guid reportId, Dictionary<string, object> parameters)
    {
        var reportData = await ExecuteReportAsync(reportId, parameters);

        var csv = new StringBuilder();

        // Add headers
        csv.AppendLine(string.Join(",", reportData.Columns.Select(EscapeCsvValue)));

        // Add data rows
        foreach (var row in reportData.Data)
        {
            var values = reportData.Columns.Select(col =>
                EscapeCsvValue(row.ContainsKey(col) ? row[col]?.ToString() ?? "" : ""));
            csv.AppendLine(string.Join(",", values));
        }

        return Encoding.UTF8.GetBytes(csv.ToString());
    }

    public async Task<byte[]> ExportReportToExcelAsync(Guid reportId, Dictionary<string, object> parameters)
    {
        var reportData = await ExecuteReportAsync(reportId, parameters);
        var report = await _reportRepository.GetAsync(reportId);

        Logger.LogInformation("check header");
        // Check if a report has custom Excel header configuration
        if (!string.IsNullOrEmpty(report.ExcelHeaderConfig))
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    PropertyNameCaseInsensitive = true
                };
                var customHeader = JsonSerializer.Deserialize<ReportExcelHeaderDto>(report.ExcelHeaderConfig, options);

                // Debug: Check if deserialization worked
                if (customHeader?.HeaderRows == null || customHeader.HeaderRows.Count == 0)
                {
                    throw new JsonException("HeaderRows is null or empty after deserialization");
                }

                return await CreateExcelFileWithCustomHeaderAsync(report, reportData, customHeader, parameters);
            }
            catch (JsonException)
            {
                // Fall back to default if JSON is invalid
                return await CreateExcelFileAsync(report, reportData);
            }
        }
        
        return await CreateExcelFileAsync(report, reportData);
    }

    public async Task<byte[]> ExportReportToExcelWithCustomHeaderAsync(Guid reportId, Dictionary<string, object> parameters, ReportExcelHeaderDto customHeader)
    {
        var reportData = await ExecuteReportAsync(reportId, parameters);
        var report = await _reportRepository.GetAsync(reportId);

        return await CreateExcelFileWithCustomHeaderAsync(report, reportData, customHeader, parameters);
    }

    public async Task<List<ReportParameterDto>> GetReportParametersAsync(Guid reportId)
    {
        var report = await _reportRepository.GetAsync(reportId);

        if (string.IsNullOrEmpty(report.Parameters))
            return new List<ReportParameterDto>();

        try
        {
            // First try to parse as JSON array
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };
            return JsonSerializer.Deserialize<List<ReportParameterDto>>(report.Parameters, options) ?? new List<ReportParameterDto>();
        }
        catch
        {
            // If JSON parsing fails, try to parse the old format: "@Year = int, @Month = int"
            return ParseLegacyParameterFormat(report.Parameters);
        }
    }

    public async Task<ReportExcelHeaderDto> GetReportExcelHeaderConfigAsync(Guid reportId)
    {
        var report = await _reportRepository.GetAsync(reportId);

        if (string.IsNullOrEmpty(report.ExcelHeaderConfig))
            return null;

        try
        {
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };
            return JsonSerializer.Deserialize<ReportExcelHeaderDto>(report.ExcelHeaderConfig, options);
        }
        catch
        {
            return null;
        }
    }

    public async Task UpdateReportExcelHeaderConfigAsync(Guid reportId, ReportExcelHeaderDto headerConfig)
    {
        var report = await _reportRepository.GetAsync(reportId);

        if (headerConfig == null)
        {
            report.ExcelHeaderConfig = null;
        }
        else
        {
            report.ExcelHeaderConfig = JsonSerializer.Serialize(headerConfig, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            });
        }

        await _reportRepository.UpdateAsync(report);
    }

    public async Task<ReportPivotConfigDto> GetReportPivotConfigAsync(Guid reportId)
    {
        var report = await _reportRepository.GetAsync(reportId);

        if (string.IsNullOrEmpty(report.PivotConfig))
            return null;

        try
        {
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };
            return JsonSerializer.Deserialize<ReportPivotConfigDto>(report.PivotConfig, options);
        }
        catch
        {
            return null;
        }
    }

    public async Task UpdateReportPivotConfigAsync(Guid reportId, ReportPivotConfigDto pivotConfig)
    {
        var report = await _reportRepository.GetAsync(reportId);

        if (pivotConfig == null)
        {
            report.PivotConfig = null;
        }
        else
        {
            report.PivotConfig = JsonSerializer.Serialize(pivotConfig, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            });
        }

        await _reportRepository.UpdateAsync(report);
    }

    private List<ReportParameterDto> ExtractReportParameters(Report report)
    {
        if (string.IsNullOrEmpty(report.Parameters))
            return new List<ReportParameterDto>();

        try
        {
            // First try to parse as JSON array
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };
            return JsonSerializer.Deserialize<List<ReportParameterDto>>(report.Parameters, options) ?? new List<ReportParameterDto>();
        }
        catch
        {
            // If JSON parsing fails, try to parse the old format: "@Year = int, @Month = int"
            return ParseLegacyParameterFormat(report.Parameters);
        }
    }

    private ReportExcelHeaderDto? ExtractReportExcelHeaderConfig(Report report)
    {
        if (string.IsNullOrEmpty(report.ExcelHeaderConfig))
            return null;

        try
        {
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };
            return JsonSerializer.Deserialize<ReportExcelHeaderDto>(report.ExcelHeaderConfig, options);
        }
        catch (JsonException ex)
        {
            // Log the specific JSON error for debugging
            // You can add logging here if needed
            return null;
        }
        catch
        {
            return null;
        }
    }

    public async Task<PivotValidationResult> ValidateReportPivotConfigAsync(Guid reportId, ReportPivotConfigDto pivotConfig)
    {
        // Execute the report to get sample data for validation
        var reportData = await ExecuteReportAsync(reportId, new Dictionary<string, object>());

        // Use the pivot processor to validate the configuration
        return await _pivotDataProcessor.ValidatePivotConfigAsync(reportData, pivotConfig);
    }

    private string ProcessQueryParameters(string query, Dictionary<string, object> parameters)
    {
        if (parameters == null || !parameters.Any())
            return query;

        var processedQuery = query;

        foreach (var param in parameters)
        {
            var placeholder = $"{{{{{param.Key}}}}}";
            var value = param.Value?.ToString() ?? "";

            // Basic SQL injection protection - escape single quotes
            value = value.Replace("'", "''");

            processedQuery = processedQuery.Replace(placeholder, value);
        }

        return processedQuery;
    }

    private void AddParametersToCommand(IDbCommand command, Dictionary<string, object> parameters)
    {
        if (parameters == null) return;

        foreach (var param in parameters)
        {
            var parameter = command.CreateParameter();
            parameter.ParameterName = $"@{param.Key}";
            parameter.Value = param.Value ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }
    }

    private async Task<byte[]> CreateExcelFileAsync(Report report, ReportPreviewDto reportData)
    {
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add(report.Name);

        // Add default title
        worksheet.Cells[1, 1].Value = report.Name;
        worksheet.Cells[1, 1].Style.Font.Size = 16;
        worksheet.Cells[1, 1].Style.Font.Bold = true;
        worksheet.Cells[1, 1, 1, reportData.Columns.Count].Merge = true;
        worksheet.Cells[1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

        // Add generated date
        worksheet.Cells[2, 1].Value = $"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
        worksheet.Cells[2, 1, 2, reportData.Columns.Count].Merge = true;

        // Add column headers starting from row 4
        int headerRow = 4;
        for (int i = 0; i < reportData.Columns.Count; i++)
        {
            worksheet.Cells[headerRow, i + 1].Value = reportData.Columns[i];
            worksheet.Cells[headerRow, i + 1].Style.Font.Bold = true;
            worksheet.Cells[headerRow, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[headerRow, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
            worksheet.Cells[headerRow, i + 1].Style.Border.BorderAround(ExcelBorderStyle.Thin);
        }

        // Add data rows
        int dataStartRow = headerRow + 1;
        for (int rowIndex = 0; rowIndex < reportData.Data.Count; rowIndex++)
        {
            var row = reportData.Data[rowIndex];
            for (int colIndex = 0; colIndex < reportData.Columns.Count; colIndex++)
            {
                var columnName = reportData.Columns[colIndex];
                var cellValue = row.ContainsKey(columnName) ? row[columnName] : null;

                var cell = worksheet.Cells[dataStartRow + rowIndex, colIndex + 1];

                // Apply auto-detected formatting
                ApplyCellValueAndFormatting(cell, cellValue, columnName, null);

                cell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }
        }

        // Auto-fit columns
        ApplyColumnWidths(worksheet, reportData.Columns, null);

        return package.GetAsByteArray();
    }

    private async Task<byte[]> CreateExcelFileWithCustomHeaderAsync(Report report, ReportPreviewDto reportData, ReportExcelHeaderDto customHeader, Dictionary<string, object>? parameters = null)
    {
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add(report.Name);

        int currentRow = 1;

        // Add custom title
        Logger.LogDebug("Header title: " + customHeader.Title);
        if (!string.IsNullOrEmpty(customHeader.Title))
        {
            var processedTitle = ReplacePlaceholders(customHeader.Title, parameters);
            worksheet.Cells[currentRow, 1].Value = processedTitle;
            worksheet.Cells[currentRow, 1, currentRow, reportData.Columns.Count].Merge = true;

            if (customHeader.TitleStyle != null)
            {
                ApplyStyleToCell(worksheet.Cells[currentRow, 1], customHeader.TitleStyle);
            }
            else
            {
                worksheet.Cells[currentRow, 1].Style.Font.Size = 16;
                worksheet.Cells[currentRow, 1].Style.Font.Bold = true;
                worksheet.Cells[currentRow, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            }
            currentRow++;
        }

        // Add subtitle
        if (!string.IsNullOrEmpty(customHeader.SubTitle))
        {
            var processedSubTitle = ReplacePlaceholders(customHeader.SubTitle, parameters);
            worksheet.Cells[currentRow, 1].Value = processedSubTitle;
            worksheet.Cells[currentRow, 1, currentRow, reportData.Columns.Count].Merge = true;

            if (customHeader.SubTitleStyle != null)
            {
                ApplyStyleToCell(worksheet.Cells[currentRow, 1], customHeader.SubTitleStyle);
            }
            else
            {
                worksheet.Cells[currentRow, 1].Style.Font.Size = 12;
                worksheet.Cells[currentRow, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            }
            currentRow++;
        }

        // Add generated date
        if (customHeader.ShowGeneratedDate)
        {
            worksheet.Cells[currentRow, 1].Value = $"Generated: {DateTime.Now.ToString(customHeader.DateFormat)}";
            worksheet.Cells[currentRow, 1, currentRow, reportData.Columns.Count].Merge = true;
            worksheet.Cells[currentRow, 1].Style.Font.Size = 9;
            currentRow++;
        }

        // Add empty row
        currentRow++;

        // Add custom header rows with colspan/rowspan support
        if (customHeader.HeaderRows?.Any() == true)
        {
            int headerStartRow = currentRow;

            foreach (var headerRow in customHeader.HeaderRows)
            {
                int currentCol = 1;

                foreach (var cell in headerRow.Cells)
                {
                    var excelRange = worksheet.Cells[currentRow, currentCol];

                    // Set cell value
                    excelRange.Value = cell.Text;

                    // Apply colspan and rowspan
                    if (cell.ColSpan > 1 || cell.RowSpan > 1)
                    {
                        var mergeRange = worksheet.Cells[currentRow, currentCol,
                            currentRow + cell.RowSpan - 1, currentCol + cell.ColSpan - 1];
                        mergeRange.Merge = true;
                        excelRange = mergeRange;
                    }

                    // Apply custom style or default header style
                    var styleToApply = cell.Style ?? customHeader.HeaderStyle;
                    if (styleToApply != null)
                    {
                        ApplyStyleToCell(excelRange, styleToApply);
                    }
                    else
                    {
                        // Default header style
                        excelRange.Style.Font.Bold = true;
                        excelRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        excelRange.Style.Fill.BackgroundColor.SetColor(Color.LightBlue);
                        excelRange.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                        excelRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        excelRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    }

                    currentCol += cell.ColSpan;
                }

                currentRow++;
            }
        }
        else
        {
            // Add default column headers
            for (int i = 0; i < reportData.Columns.Count; i++)
            {
                worksheet.Cells[currentRow, i + 1].Value = reportData.Columns[i];

                if (customHeader.HeaderStyle != null)
                {
                    ApplyStyleToCell(worksheet.Cells[currentRow, i + 1], customHeader.HeaderStyle);
                }
                else
                {
                    worksheet.Cells[currentRow, i + 1].Style.Font.Bold = true;
                    worksheet.Cells[currentRow, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[currentRow, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                    worksheet.Cells[currentRow, i + 1].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                }
            }
            currentRow++;
        }

        // Add data rows
        int dataStartRow = currentRow;
        for (int rowIndex = 0; rowIndex < reportData.Data.Count; rowIndex++)
        {
            var row = reportData.Data[rowIndex];
            for (int colIndex = 0; colIndex < reportData.Columns.Count; colIndex++)
            {
                var columnName = reportData.Columns[colIndex];
                var cellValue = row.ContainsKey(columnName) ? row[columnName] : null;

                var cell = worksheet.Cells[dataStartRow + rowIndex, colIndex + 1];

                // Check if there's a specific column configuration
                var columnConfig = customHeader.ColumnConfigs?.FirstOrDefault(c =>
                    c.ColumnName.Equals(columnName, StringComparison.OrdinalIgnoreCase));

                // Apply cell value and formatting based on column configuration or auto-detection
                ApplyCellValueAndFormatting(cell, cellValue, columnName, columnConfig);

                // Apply column-specific style or default data style
                var styleToApply = columnConfig?.Style ?? customHeader.DataStyle;
                if (styleToApply != null)
                {
                    ApplyStyleToCell(cell, styleToApply);
                }
                else
                {
                    cell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                }
            }
        }

        // Apply column widths or auto-fit columns
        ApplyColumnWidths(worksheet, reportData.Columns, customHeader.ColumnConfigs);

        return package.GetAsByteArray();
    }

    private void ApplyCellValueAndFormatting(ExcelRange cell, object? cellValue, string columnName, ReportExcelColumnConfigDto? columnConfig)
    {
        if (cellValue == null)
        {
            cell.Value = null;
            return;
        }

        // Use column configuration if available, otherwise auto-detect
        var cellType = columnConfig?.CellType ?? AutoDetectCellType(columnName, cellValue);
        var numberFormat = !string.IsNullOrEmpty(columnConfig?.NumberFormat)
            ? columnConfig.NumberFormat
            : GetDefaultNumberFormat(cellType);

        switch (cellType)
        {
            case ExcelCellType.Date:
                if (DateTime.TryParse(cellValue.ToString(), out DateTime dateValue))
                {
                    cell.Value = dateValue;
                    cell.Style.Numberformat.Format = numberFormat ?? "dd/mm/yyyy";
                }
                else if (double.TryParse(cellValue.ToString(), out double excelDate))
                {
                    try
                    {
                        var convertedDate = DateTime.FromOADate(excelDate);
                        cell.Value = convertedDate;
                        cell.Style.Numberformat.Format = numberFormat ?? "dd/mm/yyyy";
                    }
                    catch
                    {
                        cell.Value = cellValue;
                    }
                }
                else
                {
                    cell.Value = cellValue;
                }
                break;

            case ExcelCellType.DateTime:
                if (DateTime.TryParse(cellValue.ToString(), out DateTime dateTimeValue))
                {
                    cell.Value = dateTimeValue;
                    cell.Style.Numberformat.Format = numberFormat ?? "dd/mm/yyyy hh:mm:ss";
                }
                else
                {
                    cell.Value = cellValue;
                }
                break;

            case ExcelCellType.Number:
                if (decimal.TryParse(cellValue.ToString(), out decimal decimalValue))
                {
                    cell.Value = decimalValue;
                    cell.Style.Numberformat.Format = numberFormat ?? "#,##0.00";
                }
                else
                {
                    cell.Value = cellValue;
                }
                break;

            case ExcelCellType.Currency:
                if (decimal.TryParse(cellValue.ToString(), out decimal currencyValue))
                {
                    cell.Value = currencyValue;
                    cell.Style.Numberformat.Format = numberFormat ?? "$#,##0.00";
                }
                else
                {
                    cell.Value = cellValue;
                }
                break;

            case ExcelCellType.Percentage:
                if (decimal.TryParse(cellValue.ToString(), out decimal percentValue))
                {
                    cell.Value = percentValue / 100; // Excel expects percentage as decimal
                    cell.Style.Numberformat.Format = numberFormat ?? "0.00%";
                }
                else
                {
                    cell.Value = cellValue;
                }
                break;

            case ExcelCellType.Boolean:
                if (bool.TryParse(cellValue.ToString(), out bool boolValue))
                {
                    cell.Value = boolValue ? "Yes" : "No";
                }
                else
                {
                    cell.Value = cellValue;
                }
                break;

            case ExcelCellType.Time:
                if (TimeSpan.TryParse(cellValue.ToString(), out TimeSpan timeValue))
                {
                    cell.Value = timeValue;
                    cell.Style.Numberformat.Format = numberFormat ?? "hh:mm:ss";
                }
                else
                {
                    cell.Value = cellValue;
                }
                break;

            case ExcelCellType.Text:
            default:
                cell.Value = cellValue;
                if (!string.IsNullOrEmpty(numberFormat))
                {
                    cell.Style.Numberformat.Format = numberFormat;
                }
                break;
        }

        // Apply conditional formatting if rules are defined
        if (columnConfig?.ConditionalRules?.Any() == true)
        {
            var conditionalBackgroundColor = _conditionalFormattingService.EvaluateConditionalFormatting(cellValue, columnConfig.ConditionalRules);
            if (!string.IsNullOrEmpty(conditionalBackgroundColor))
            {
                try
                {
                    cell.Style.Fill.PatternType = ExcelFillStyle.Solid;
                    cell.Style.Fill.BackgroundColor.SetColor(ColorTranslator.FromHtml(conditionalBackgroundColor));
                }
                catch
                {
                    // Ignore invalid color codes to prevent breaking the export
                }
            }
        }
    }

    private ExcelCellType AutoDetectCellType(string columnName, object cellValue)
    {
        // Auto-detect based on column name
        var lowerColumnName = columnName.ToLower();

        if (lowerColumnName.Contains("date"))
        {
            return lowerColumnName.Contains("time") ? ExcelCellType.DateTime : ExcelCellType.Date;
        }

        if (lowerColumnName.Contains("time"))
        {
            return ExcelCellType.Time;
        }

        if (lowerColumnName.Contains("percent") || lowerColumnName.Contains("%"))
        {
            return ExcelCellType.Percentage;
        }

        if (lowerColumnName.Contains("price") || lowerColumnName.Contains("cost") ||
            lowerColumnName.Contains("amount") || lowerColumnName.Contains("currency"))
        {
            return ExcelCellType.Currency;
        }

        // Auto-detect based on value type
        if (cellValue != null)
        {
            var valueString = cellValue.ToString();

            if (DateTime.TryParse(valueString, out _))
            {
                return ExcelCellType.Date;
            }

            if (decimal.TryParse(valueString, out _))
            {
                return ExcelCellType.Number;
            }

            if (bool.TryParse(valueString, out _))
            {
                return ExcelCellType.Boolean;
            }
        }

        return ExcelCellType.Text;
    }

    private string? GetDefaultNumberFormat(ExcelCellType cellType)
    {
        return cellType switch
        {
            ExcelCellType.Date => "dd/mm/yyyy",
            ExcelCellType.DateTime => "dd/mm/yyyy hh:mm:ss",
            ExcelCellType.Number => "#,##0.00",
            ExcelCellType.Currency => "$#,##0.00",
            ExcelCellType.Percentage => "0.00%",
            ExcelCellType.Time => "hh:mm:ss",
            _ => null
        };
    }

    private void ApplyColumnWidths(ExcelWorksheet worksheet, List<string> columns, List<ReportExcelColumnConfigDto>? columnConfigs)
    {
        for (int i = 0; i < columns.Count; i++)
        {
            var columnName = columns[i];
            var columnConfig = columnConfigs?.FirstOrDefault(c =>
                c.ColumnName.Equals(columnName, StringComparison.OrdinalIgnoreCase));

            if (columnConfig?.Width > 0)
            {
                worksheet.Column(i + 1).Width = columnConfig.Width;
            }
            else
            {
                // Auto-fit this column
                worksheet.Column(i + 1).AutoFit();
            }
        }
    }

    private void ApplyStyleToCell(ExcelRange cell, ReportExcelStyleDto style)
    {
        if (style == null) return;

        // Font settings
        if (!string.IsNullOrEmpty(style.FontName))
            cell.Style.Font.Name = style.FontName;

        if (style.FontSize > 0)
            cell.Style.Font.Size = style.FontSize;

        cell.Style.Font.Bold = style.Bold;

        // Colors
        if (!string.IsNullOrEmpty(style.BackgroundColor))
        {
            cell.Style.Fill.PatternType = ExcelFillStyle.Solid;
            cell.Style.Fill.BackgroundColor.SetColor(ColorTranslator.FromHtml(style.BackgroundColor));
        }

        if (!string.IsNullOrEmpty(style.FontColor))
        {
            cell.Style.Font.Color.SetColor(ColorTranslator.FromHtml(style.FontColor));
        }

        // Alignment
        cell.Style.HorizontalAlignment = style.HorizontalAlignment?.ToLower() switch
        {
            "center" => ExcelHorizontalAlignment.Center,
            "right" => ExcelHorizontalAlignment.Right,
            _ => ExcelHorizontalAlignment.Left
        };

        cell.Style.VerticalAlignment = style.VerticalAlignment?.ToLower() switch
        {
            "middle" => ExcelVerticalAlignment.Center,
            "bottom" => ExcelVerticalAlignment.Bottom,
            _ => ExcelVerticalAlignment.Top
        };

        // Text wrapping
        cell.Style.WrapText = style.WrapText;

        // Border
        if (style.Border)
        {
            cell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
        }
    }

    private string EscapeCsvValue(string value)
    {
        if (string.IsNullOrEmpty(value))
            return "";

        // Check if the value contains special characters that require escaping
        bool needsEscaping = value.Contains(',') || value.Contains('"') || value.Contains('\n') || value.Contains('\r');

        if (needsEscaping)
        {
            // Escape double quotes by doubling them
            value = value.Replace("\"", "\"\"");

            // Wrap the entire value in double quotes
            return $"\"{value}\"";
        }

        return value;
    }

    /// <summary>
    /// Replace placeholders in text with actual parameter values
    /// </summary>
    /// <param name="text">Text containing placeholders like {{year}}, {{month}}</param>
    /// <param name="parameters">Dictionary of parameter values</param>
    /// <returns>Text with placeholders replaced</returns>
    private string ReplacePlaceholders(string text, Dictionary<string, object>? parameters)
    {
        if (string.IsNullOrEmpty(text) || parameters == null || !parameters.Any())
            return text;

        var processedText = text;

        foreach (var param in parameters)
        {
            var placeholder = $"{{{{{param.Key}}}}}";
            var value = param.Value?.ToString() ?? "";
            processedText = processedText.Replace(placeholder, value);
        }

        return processedText;
    }

    /// <summary>
    /// Parse legacy parameter format like "@Year = int, @Month = int"
    /// </summary>
    /// <param name="parametersString">Legacy format parameter string</param>
    /// <returns>List of ReportParameterDto</returns>
    private List<ReportParameterDto> ParseLegacyParameterFormat(string parametersString)
    {
        var parameters = new List<ReportParameterDto>();

        if (string.IsNullOrEmpty(parametersString))
            return parameters;

        try
        {
            // Split by comma and parse each parameter
            var paramParts = parametersString.Split(',', StringSplitOptions.RemoveEmptyEntries);

            foreach (var part in paramParts)
            {
                var trimmedPart = part.Trim();

                // Parse format like "@Year = int" or "@Month = int"
                if (trimmedPart.StartsWith("@") && trimmedPart.Contains("="))
                {
                    var equalIndex = trimmedPart.IndexOf('=');
                    var paramName = trimmedPart.Substring(1, equalIndex - 1).Trim(); // Remove @ and get name
                    var paramType = trimmedPart.Substring(equalIndex + 1).Trim();

                    parameters.Add(new ReportParameterDto
                    {
                        Name = paramName.ToLower(), // Convert to lowercase to match convention
                        Type = paramType,
                        Required = true,
                        DefaultValue = "",
                        Description = $"Parameter {paramName}"
                    });
                }
            }
        }
        catch
        {
            // If parsing fails, return empty list
        }

        return parameters;
    }
}