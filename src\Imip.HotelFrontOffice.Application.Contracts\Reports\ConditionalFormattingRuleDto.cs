namespace Imip.HotelFrontOffice.Reports;

/// <summary>
/// Defines a conditional formatting rule for Excel cells based on cell values
/// </summary>
public class ConditionalFormattingRuleDto
{
    /// <summary>
    /// The type of condition to evaluate
    /// </summary>
    public ConditionalFormattingType ConditionType { get; set; }

    /// <summary>
    /// The operator for comparison (for numeric and text comparisons)
    /// </summary>
    public ConditionalFormattingOperator Operator { get; set; }

    /// <summary>
    /// The value to compare against (for specific value matches and comparisons)
    /// </summary>
    public string? ComparisonValue { get; set; }

    /// <summary>
    /// The text pattern to search for (for text pattern matching)
    /// </summary>
    public string? TextPattern { get; set; }

    /// <summary>
    /// The background color to apply when the condition is met (hex color code)
    /// </summary>
    public string BackgroundColor { get; set; } = default!;

    /// <summary>
    /// Priority order for rule evaluation (lower numbers have higher priority)
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// Whether this rule is case-sensitive for text comparisons
    /// </summary>
    public bool CaseSensitive { get; set; } = false;
}

/// <summary>
/// Types of conditional formatting conditions
/// </summary>
public enum ConditionalFormattingType
{
    /// <summary>
    /// Match specific values exactly
    /// </summary>
    SpecificValue,

    /// <summary>
    /// Numeric comparisons (greater than, less than, etc.)
    /// </summary>
    NumericComparison,

    /// <summary>
    /// Text pattern matching (contains, starts with, ends with)
    /// </summary>
    TextPattern
}

/// <summary>
/// Operators for conditional formatting comparisons
/// </summary>
public enum ConditionalFormattingOperator
{
    // For SpecificValue and general comparisons
    Equals,
    NotEquals,

    // For NumericComparison
    GreaterThan,
    GreaterThanOrEqual,
    LessThan,
    LessThanOrEqual,

    // For TextPattern
    Contains,
    StartsWith,
    EndsWith,
    NotContains
}
