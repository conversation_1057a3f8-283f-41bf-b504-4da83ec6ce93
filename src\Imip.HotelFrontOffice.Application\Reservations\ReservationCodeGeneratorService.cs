﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Companies;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace Imip.HotelFrontOffice.Reservations;

public class ReservationCodeGeneratorService : DomainService, IReservationCodeGeneratorService
{
    private readonly IRepository<Reservation, Guid> _repository;
    private readonly IRepository<Company, Guid> _companyRepository;
    private readonly ILogger<ReservationCodeGeneratorService> _logger;

    public ReservationCodeGeneratorService(
        IRepository<Reservation, Guid> repository,
        IRepository<Company, Guid> companyRepository,
        ILogger<ReservationCodeGeneratorService> logger)
    {
        _repository = repository;
        _companyRepository = companyRepository;
        _logger = logger;
    }

    public async Task<string> GenerateReservationCode(Guid? companyId)
    {
        var company = companyId.HasValue
            ? await _companyRepository.GetAsync(companyId.Value)
            : null;

        if (company == null)
        {
            throw new ArgumentException("Invalid company ID", nameof(companyId));
        }

        if (string.IsNullOrEmpty(company.ReservationCodePrefix))
        {
            throw new InvalidOperationException("Company does not have a reservation code prefix set.");
        }

        // Format: RES-YYYYMM-0001 (increments each month)
        var currentDate = DateTime.Now;
        var yearMonth = currentDate.ToString("yyyyMM");
        var prefix = $"{company.ReservationCodePrefix}{yearMonth}-";

        // Get the last reservation code for the current month
        var lastReservationCode = await GetLastReservationCodeForMonth(company, currentDate);

        // Extract the sequence number from the last code or start with 0
        int sequenceNumber = 0;
        if (!string.IsNullOrEmpty(lastReservationCode) && lastReservationCode.StartsWith(prefix))
        {
            // Extract the sequence part (after the prefix)
            var lastSequencePart = lastReservationCode[(prefix.Length)..];
            if (int.TryParse(lastSequencePart, out int lastSequence))
            {
                sequenceNumber = lastSequence;
            }
        }

        // Increment the sequence number
        sequenceNumber++;

        // Format the new code with a 3-digit sequence number
        return $"{prefix}{sequenceNumber:D3}";
    }

    private async Task<string?> GetLastReservationCodeForMonth(Company company, DateTime date)
    {
        try
        {
            var yearMonth = date.ToString("yyyyMM");
            var prefix = $"{company.ReservationCodePrefix}{yearMonth}-";

            // Query to find the last reservation code for the current month
            var query = await _repository.GetQueryableAsync();
            var lastReservation = await query
                .Where(r => r.ReservationCode.StartsWith(prefix))
                .OrderByDescending(r => r.ReservationCode)
                .FirstOrDefaultAsync();

            return lastReservation?.ReservationCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting last reservation code: {Message}", ex.Message);
            return string.Empty;
        }
    }
}
