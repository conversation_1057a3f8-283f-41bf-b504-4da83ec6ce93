using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.Guests;

/// <summary>
/// DTO for guest attachment with base64 encoded file content
/// </summary>
public class GuestAttachmentDto
{
    /// <summary>
    /// The name of the file
    /// </summary>
    [Required]
    [StringLength(256)]
    public required string FileName { get; set; }

    /// <summary>
    /// The content type of the file (e.g., "application/pdf", "image/jpeg")
    /// </summary>
    [Required]
    [StringLength(128)]
    public required string ContentType { get; set; }

    /// <summary>
    /// The file content as a base64 encoded string
    /// </summary>
    [Required]
    public required string Base64Content { get; set; }

    /// <summary>
    /// Optional description of the file
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }
}
