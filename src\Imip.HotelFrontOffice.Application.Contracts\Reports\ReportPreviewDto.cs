using System;
using System.Collections.Generic;

namespace Imip.HotelFrontOffice.Reports;

public class ReportPreviewDto
{
    public string ReportName { get; set; } = default!;
    public List<Dictionary<string, object>> Data { get; set; } = new();
    public List<string> Columns { get; set; } = new();
    public int TotalRows { get; set; }
    public DateTime ExecutedAt { get; set; }
    public List<ReportParameterDto>? Parameters { get; set; }
    public ReportExcelHeaderDto? ExcelHeaderConfig { get; set; }
}
