﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.ReportRooms;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.Application.Contracts;

public interface IReportRoomsAppService : IApplicationService
{
    Task<List<ReportRoomsDTO>> GetReportRoomsAsync(DateTime date);
    Task<List<ReportRoomsDTO>> GetRoomTypesReportByDateAsync(DateTime date);
}