﻿using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.Permissions;

public class DynamicPolicyAuthorizationHandler : AuthorizationHandler<DynamicPolicyRequirement>, ITransientDependency
{
    // We no longer use a static policy-to-role mapping
    // All permissions are checked directly with the Identity Server API

    private readonly ILogger<DynamicPolicyAuthorizationHandler> _logger;
    private readonly ApplicationConfigurationService _applicationConfigurationService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DynamicPolicyAuthorizationHandler(
        ILogger<DynamicPolicyAuthorizationHandler> logger,
        ApplicationConfigurationService applicationConfigurationService,
        IHttpContextAccessor httpContextAccessor)
    {
        _logger = logger;
        _applicationConfigurationService = applicationConfigurationService;
        _httpContextAccessor = httpContextAccessor;
    }

    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        DynamicPolicyRequirement requirement)
    {
        if (context.User == null)
        {
            _logger.LogWarning("User is null when checking policy: {PolicyName}", requirement.PolicyName);
            return;
        }

        // Get the access token from the HttpContext
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext != null)
        {
            var accessToken = await GetAccessTokenAsync(httpContext);

            // Check authentication status
            var authResult = await httpContext.AuthenticateAsync();
            if (!authResult.Succeeded)
            {
                _logger.LogWarning("Authentication failed: {FailureMessage}", authResult.Failure?.Message);
            }

            if (!string.IsNullOrEmpty(accessToken))
            {
                // Check if the permission is granted in the application configuration
                var grantedPolicies = await _applicationConfigurationService.GetGrantedPoliciesAsync(accessToken);

                // Try with the original policy name
                if (grantedPolicies.TryGetValue(requirement.PolicyName, out var isGranted) && isGranted)
                {
                    context.Succeed(requirement);
                    return;
                }

                // Try with the "IdentityServer." prefix if the policy doesn't have it
                if (!requirement.PolicyName.StartsWith("IdentityServer."))
                {
                    var identityServerPolicyName = "IdentityServer." + requirement.PolicyName;
                    if (grantedPolicies.TryGetValue(identityServerPolicyName, out isGranted) && isGranted)
                    {
                        _logger.LogDebug("Policy {PolicyName} is granted as {IdentityServerPolicyName}",
                            requirement.PolicyName, identityServerPolicyName);
                        context.Succeed(requirement);
                        return;
                    }
                }

                _logger.LogDebug("Policy {PolicyName} not found in application configuration, checking roles",
                    requirement.PolicyName);

                // Check if the user has the required roles for this policy
                if (HasRequiredRolesForPolicy(context.User, requirement.PolicyName))
                {
                    _logger.LogDebug("Policy {PolicyName} is granted based on user roles", requirement.PolicyName);
                    context.Succeed(requirement);
                    return;
                }
            }
        }

        // Special case for the Default policy requirement
        if (requirement.PolicyName == "Default")
        {
            _logger.LogDebug("Default policy requirement - succeeding as this is just a placeholder");
            context.Succeed(requirement);
            return;
        }

        // If we couldn't check with the application configuration, the permission is denied
        _logger.LogWarning("Policy {PolicyName} is denied", requirement.PolicyName);

        // Store the denied policy in the HttpContext for our middleware to use
        if (_httpContextAccessor.HttpContext != null)
        {
            _httpContextAccessor.HttpContext.Items["DeniedPermission"] = requirement.PolicyName;
        }
    }

    /// <summary>
    /// Helper method to get access token from HttpContext
    /// </summary>
    private async Task<string?> GetAccessTokenAsync(HttpContext httpContext)
    {
        // First try to get the token from the authentication properties
        var accessToken = await httpContext.GetTokenAsync("access_token");

        // If the token is not found in the authentication properties, try to extract it from the Authorization header
        if (string.IsNullOrEmpty(accessToken))
        {
            var authHeader = httpContext.Request.Headers.Authorization.FirstOrDefault();
            if (!string.IsNullOrEmpty(authHeader) &&
                authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                accessToken = authHeader["Bearer ".Length..].Trim();
                _logger.LogDebug("Extracted access token from Authorization header");
            }
        }

        // Only log token length for security
        if (!string.IsNullOrEmpty(accessToken))
        {
            _logger.LogDebug("Retrieved access token: {TokenLength} characters", accessToken.Length);
        }

        return accessToken;
    }

    /// <summary>
    /// Checks if the user has the required roles for a specific policy.
    /// This is a more dynamic approach than hardcoding specific policies.
    /// </summary>
    /// <param name="user">The ClaimsPrincipal representing the user</param>
    /// <param name="policyName">The name of the policy to check</param>
    /// <returns>True if the user has the required roles for the policy, false otherwise</returns>
    private bool HasRequiredRolesForPolicy(ClaimsPrincipal user, string policyName)
    {
        // Since we no longer use a static policy-to-role mapping,
        // we'll implement a simple fallback for admin users only

        // Get all roles the user has
        var roles = user.Claims
            .Where(c => c.Type == ClaimTypes.Role)
            .Select(c => c.Value.ToLowerInvariant())
            .ToList();

        // Admin users have access to everything
        if (roles.Contains("admin"))
        {
            _logger.LogDebug("User has admin role, granting access to policy: {PolicyName}", policyName);
            return true;
        }

        // For all other users, we rely on the Identity Server API
        _logger.LogDebug("User does not have admin role, deferring to Identity Server for policy: {PolicyName}",
            policyName);
        return false;
    }
}