using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.SettingManagement;
using Volo.Abp.Settings;

namespace Imip.HotelFrontOffice.Settings;

[Authorize(WismaAppPermissions.PolicySettings.Default)]
public class MarketCodeSettingsAppService : HotelFrontOfficeAppService, IMarketCodeSettingsAppService
{
    private readonly ISettingProvider _settingProvider;
    private readonly ISettingManager _settingManager;
    private readonly ILogger<MarketCodeSettingsAppService> _logger;

    public MarketCodeSettingsAppService(
        ISettingProvider settingProvider,
        ISettingManager settingManager,
        ILogger<MarketCodeSettingsAppService> logger)
    {
        _settingProvider = settingProvider;
        _settingManager = settingManager;
        _logger = logger;
    }

    [Authorize(WismaAppPermissions.PolicySettings.View)]
    public async Task<MarketCodeSettingsDto> GetSettingsAsync()
    {
        var marketCode = await _settingProvider.GetOrNullAsync(HotelFrontOfficeSettings.SettingMarketCode.MarketCode);
        return new MarketCodeSettingsDto
        {
            MarketCode = marketCode != null ? int.Parse(marketCode) : 32251 // Default value if not set,
        };
    }

    [Authorize(WismaAppPermissions.PolicySettings.Edit)]
    public async Task UpdateSettingsAsync(MarketCodeSettingsDto input)
    {
        try
        {
            // Update the market code setting
            await _settingManager.SetGlobalAsync(HotelFrontOfficeSettings.SettingMarketCode.MarketCode, input.MarketCode.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating market code settings");
            throw;
        }
    }
}