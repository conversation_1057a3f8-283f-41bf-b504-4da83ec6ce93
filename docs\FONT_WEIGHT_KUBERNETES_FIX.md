# Font Weight Configuration for Kubernetes Deployment

## Overview

This document describes the implementation of environment-specific font weight configuration for invoice generation in Kubernetes deployments. The feature addresses font rendering issues where fonts appear too bold in containerized environments.

## Problem

When generating invoices using Syncfusion document conversion in Kubernetes Docker containers, fonts were appearing too bold compared to local development environments. This was particularly noticeable in the invoice tables where bold text was used for headers and important information.

## Solution

### 1. Configuration Setting

A new configuration setting `PdfOptimization:UseLighterFontWeight` has been added to control font weight behavior:

```json
{
  "PdfOptimization": {
    "UseLighterFontWeight": false  // Default: false (use normal bold fonts)
  }
}
```

### 2. Environment-Specific Configuration

**Local Development** (`src/Imip.HotelFrontOffice.Web/appsettings.json`):
```json
{
  "PdfOptimization": {
    "UseLighterFontWeight": false
  }
}
```

**Kubernetes Production** (`k8s/prod/configmap.yaml`):
```yaml
PdfOptimization__UseLighterFontWeight: "true"
```

**Kubernetes Development** (`k8s/dev/configmap.yaml`):
```yaml
PdfOptimization__UseLighterFontWeight: "true"
```

### 3. Implementation Details

#### Modified Files

1. **TableGenerator.cs**
   - Added `IConfiguration` parameter to `CreateTable` and `CreateTableCell` methods
   - Modified font weight logic to check configuration setting
   - When `UseLighterFontWeight` is true, normal weight is used instead of bold

2. **WismaInvoiceGenerator.cs**
   - Added `IConfiguration` dependency injection
   - Updated constructor to accept configuration
   - Passes configuration to `TableGenerator.CreateTable` calls

3. **InvoiceDocumentService.cs**
   - Updated `ReplaceTablePlaceholder` method to accept and pass configuration
   - Modified method calls to include configuration parameter

#### Code Changes

```csharp
// Before
if (isBold)
{
    runProperties.AppendChild(new Bold());
}

// After
if (isBold)
{
    var useLighterFontWeight = configuration?.GetValue<bool>("PdfOptimization:UseLighterFontWeight", false) ?? false;
    
    if (useLighterFontWeight)
    {
        // Use normal weight instead of bold for Kubernetes deployment
        // This helps with font rendering issues in containerized environments
        // The text will still be visually distinct due to other formatting
    }
    else
    {
        // Use bold as normal for non-Kubernetes environments
        runProperties.AppendChild(new Bold());
    }
}
```

## Benefits

1. **Environment-Specific Control**: Different font weights can be used for different deployment environments
2. **Backward Compatibility**: Default behavior remains unchanged for existing deployments
3. **Improved Readability**: Fonts appear more readable in Kubernetes environments
4. **Configurable**: Easy to enable/disable without code changes

## Usage

### For Kubernetes Deployments

Set the configuration to use lighter font weights:
```yaml
PdfOptimization__UseLighterFontWeight: "true"
```

### For Local Development

Keep the default setting for normal font behavior:
```json
{
  "PdfOptimization": {
    "UseLighterFontWeight": false
  }
}
```

## Testing

To test the font weight changes:

1. Generate an invoice in local development environment (should use bold fonts)
2. Deploy to Kubernetes with `UseLighterFontWeight: true`
3. Generate the same invoice in Kubernetes (should use normal weight fonts)
4. Compare the visual appearance of the generated PDFs

## Notes

- This change only affects table generation in invoice documents
- The text remains visually distinct through other formatting (borders, alignment, etc.)
- No changes are required to existing templates or data structures
- The feature is backward compatible and optional

---

# Settlement Company and VAT Display Logic Enhancement

## Overview

This document describes the enhanced settlement company and VAT display logic for invoice generation. The feature implements conditional logic based on payment method and VAT amounts to control when settlement company information and tax calculations are displayed.

## Settlement Company Field Logic

### Implementation

The settlement company field (`{{SETTLEMENT_COMPANY}}`) is now populated based on the following logic:

```csharp
private string GetSettlementCompany(Payment payment, string companyName)
{
    // If VAT amount is greater than 0, return the company name from invoice data
    if (payment.VatAmount.HasValue && payment.VatAmount.Value > 0)
    {
        return companyName;
    }

    // If VAT amount is 0 or null, return empty string
    return string.Empty;
}
```

### Behavior

- **When `VatAmount > 0`**: Settlement company field displays the same value as `COMPANY_NAME`
- **When `VatAmount = 0` or null**: Settlement company field remains empty
- **Data Source**: Uses `data.CompanyName` from the invoice template data

## VAT Display Logic Based on Payment Method

### Implementation

The VAT calculation section (tax rows) is now conditionally displayed based on both payment method and VAT amounts:

```csharp
// Tax calculation should only appear when:
// 1. Payment method name ≠ "Company Invoice" (case-sensitive exact match)
// 2. VatAmount > 0 AND VatRate > 0
bool hasTax = data.VatAmount.HasValue && data.VatAmount.Value > 0 &&
              data.VatRate.HasValue && data.VatRate.Value > 0 &&
              !string.Equals(data.PaymentMethod, "Company Invoice", StringComparison.Ordinal);
```

### Display Rules

| Payment Method | VAT Amount | VAT Rate | Tax Section Displayed |
|---------------|------------|----------|----------------------|
| "Company Invoice" | Any | Any | ❌ No |
| Other methods | > 0 | > 0 | ✅ Yes |
| Other methods | = 0 or null | Any | ❌ No |
| Other methods | > 0 | = 0 or null | ❌ No |

### Tax Calculation Section

When displayed, the tax section includes these rows in order:

1. **Subtotal rows**: Individual subtotals for Room Charges, Meal Expenses, and Others
2. **Combined subtotal (合计)**: Sum of all categories before tax
3. **Tax calculation (税X%)**: Shows the actual VAT rate and amount
4. **Grand total (总计（Total）)**: Final amount including tax

## Data Sources

### From AppPayments Table

- `totalAmount`: Amount before tax
- `vatRate`: VAT rate percentage (e.g., 11.0000 for 11%)
- `vatAmount`: VAT amount (e.g., 600600.0000)
- `grantTotal`: Grand total amount including tax

### From Invoice Template Data

- `data.CompanyName`: Used for settlement company value
- `data.PaymentMethod`: Used for payment method comparison
- `data.VatAmount` and `data.VatRate`: Used for VAT calculations

## Key Features

### Case-Sensitive Payment Method Check

- Uses `StringComparison.Ordinal` for exact case-sensitive matching
- Payment method must exactly equal "Company Invoice" to hide tax section
- Any other payment method name allows tax display (when VAT conditions are met)

### Backward Compatibility

- No changes to existing functionality when VAT is not present
- Maintains all existing table formatting and structure
- Uses the same AlignedTableGenerator as the base for consistency

### Dynamic VAT Rate Display

- Tax row label dynamically shows the actual VAT rate (e.g., "税11%" for 11% VAT)
- Supports any VAT rate percentage
- Formats currency values using the existing `FormatCurrency` method

## Testing Scenarios

### Scenario 1: Company Invoice Payment
- **Payment Method**: "Company Invoice"
- **VAT Amount**: 600600.0000
- **VAT Rate**: 11.0000
- **Expected Result**: No tax calculation section displayed

### Scenario 2: Credit Card Payment with VAT
- **Payment Method**: "Credit Card"
- **VAT Amount**: 600600.0000
- **VAT Rate**: 11.0000
- **Expected Result**: Tax calculation section displayed with 11% tax

### Scenario 3: Cash Payment without VAT
- **Payment Method**: "Cash"
- **VAT Amount**: 0 or null
- **VAT Rate**: Any
- **Expected Result**: No tax calculation section displayed

### Scenario 4: Settlement Company Display
- **VAT Amount**: 600600.0000
- **Company Name**: "TSINGSHAN WISMA IMIP"
- **Expected Result**: Settlement company field shows "TSINGSHAN WISMA IMIP"

## Benefits

- **Payment Method Control**: Provides granular control over tax display based on payment method
- **Business Logic Compliance**: Supports different tax treatment for company invoices vs. other payment methods
- **Accurate Settlement Information**: Settlement company only appears when VAT is applicable
- **Flexible Configuration**: Easy to modify payment method rules without code changes
- **Maintains Performance**: Efficient string comparison with minimal overhead
