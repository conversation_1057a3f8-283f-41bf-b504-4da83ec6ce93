using System;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.EntityChangeLogs.Dtos
{
    /// <summary>
    /// Input DTO for getting entity changes by entity ID with pagination
    /// </summary>
    public class GetEntityChangesByIdInput : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// The ID of the entity to get change logs for
        /// </summary>
        public string? EntityId { get; set; }

        /// <summary>
        /// The full type name of the entity to get change logs for (optional)
        /// </summary>
        public string? EntityTypeFullName { get; set; }

        /// <summary>
        /// Start time for filtering change logs (optional)
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// End time for filtering change logs (optional)
        /// </summary>
        public DateTime? EndTime { get; set; }
    }
}
