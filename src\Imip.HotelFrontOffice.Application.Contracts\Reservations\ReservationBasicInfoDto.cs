using Imip.HotelFrontOffice.DiningOptions;
using Imip.HotelFrontOffice.Master.Company;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.ReservationTypes;
using System;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.Reservations;

/// <summary>
/// A simplified version of ReservationsDto without the ReservationDetails collection
/// to avoid circular references in JSON serialization
/// </summary>
public class ReservationBasicInfoDto : AuditedEntityDto<Guid>
{
    public required string ReservationCode { get; set; }
    public string? GroupCode { get; set; }
    public string? BookerName { get; set; }
    public string? BookerIdentityNumber { get; set; }
    public string? BookerPhoneNumber { get; set; }
    public string? BookerEmail { get; set; }
    public DateTime ArrivalDate { get; set; }
    public int Days { get; set; }

    /// <summary>
    /// Legacy attachment field (for backward compatibility)
    /// </summary>
    public string? Attachment { get; set; }

    // IDs and expanded objects for related entities
    public Guid? CompanyId { get; set; }
    public CompanyDto? Company { get; set; }

    public Guid StatusId { get; set; }
    public MasterStatusDto Status { get; set; } = default!;

    public Guid ReservationTypeId { get; set; }
    public ReservationTypesDto ReservationType { get; set; } = default!;

    public Guid? DiningOptionsId { get; set; }
    public DiningOptionsDto? DiningOptions { get; set; }

    public Guid? PaymentMethodId { get; set; }

    // Note: ReservationDetails collection is intentionally omitted to break circular references
}
