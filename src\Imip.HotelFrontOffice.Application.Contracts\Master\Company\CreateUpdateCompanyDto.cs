﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.Master.Company;

public class CreateUpdateCompanyDto
{
    [Required]
    [StringLength(128)]
    public required string Name { get; set; }

    public string? Address { get; set; }

    [Required]
    [StringLength(100)]
    public string? Alias { get; set; }

    [Required]
    [StringLength(100)]
    public string? ReservationCodePrefix { get; set; }

    public Guid? CompanyTypeId { get; set; }
}