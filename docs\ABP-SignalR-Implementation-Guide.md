# ABP Framework SignalR Implementation Guide

This implementation follows **ABP Framework's official SignalR standards** as documented at: https://abp.io/docs/latest/framework/real-time/signalr

## 🎯 **ABP SignalR Benefits**

### **What ABP Provides Over Standard SignalR:**

1. **Automatic Hub Registration**: ABP automatically registers all hubs to dependency injection
2. **Automatic Hub Mapping**: ABP automatically maps hub endpoints with conventional routing
3. **Built-in Authentication**: Integrated with ABP's authentication system
4. **Base Classes**: `AbpHub` provides useful properties like `CurrentUser`, `Logger`, etc.
5. **Conventional Routing**: Automatic `/signalr-hubs/{hub-name}` URL pattern
6. **Dependency Injection**: Full DI support for hub classes

## 🔧 **Implementation Following ABP Standards**

### **1. Backend Implementation**

#### **Hub Class (ABP Compliant)**
```csharp
[Authorize]
[HubRoute("/signalr-hubs/room-status")] // Optional: Custom route
public class RoomStatusHub : AbpHub // ← Inherit from AbpHub, not Hub
{
    // No constructor needed - ABP provides Logger, CurrentUser, etc.
    
    public override async Task OnConnectedAsync()
    {
        // Use ABP's built-in properties
        var userId = CurrentUser.Id;
        var userName = CurrentUser.UserName;
        
        Logger.LogInformation("User {UserName} connected", userName);
        
        // Add to groups
        await Groups.AddToGroupAsync(Context.ConnectionId, "RoomStatusUpdates");
        
        await base.OnConnectedAsync();
    }
    
    public async Task JoinRoomGroup(string roomId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"Room_{roomId}");
        Logger.LogDebug("User {UserName} joined room group", CurrentUser.UserName);
    }
}
```

#### **Key ABP Features Used:**
- ✅ **`AbpHub` base class**: Provides `CurrentUser`, `Logger`, `L` (localization)
- ✅ **`[HubRoute]` attribute**: Custom route definition (optional)
- ✅ **`[Authorize]` attribute**: ABP authentication integration
- ✅ **Automatic registration**: No manual DI registration needed
- ✅ **Automatic mapping**: No manual endpoint mapping needed

#### **Notification Service (ABP Compliant)**
```csharp
public class RoomStatusNotificationService : IRoomStatusNotificationService, ITransientDependency
{
    private readonly IHubContext<RoomStatusHub> _hubContext;
    
    public RoomStatusNotificationService(IHubContext<RoomStatusHub> hubContext)
    {
        _hubContext = hubContext;
    }
    
    public async Task NotifyRoomStatusChangedAsync(RoomStatusChangeNotificationDto notification)
    {
        await _hubContext.Clients.Group("RoomStatusUpdates")
            .SendAsync("RoomStatusChanged", notification);
    }
}
```

### **2. Module Configuration (ABP Standard)**

#### **Package Installation**
```bash
# Use ABP CLI (recommended)
abp add-package Volo.Abp.AspNetCore.SignalR

# Or manually add to .csproj
<PackageReference Include="Volo.Abp.AspNetCore.SignalR" Version="9.0.4" />
```

#### **Module Dependency**
```csharp
[DependsOn(typeof(AbpAspNetCoreSignalRModule))]
public class YourApplicationModule : AbpModule
{
    // ABP handles SignalR configuration automatically
}
```

#### **CORS Configuration (if needed)**
```csharp
private void ConfigureCors(IServiceCollection services, IConfiguration configuration)
{
    services.AddCors(options =>
    {
        options.AddDefaultPolicy(builder =>
        {
            builder
                .WithOrigins("http://localhost:3000", "https://localhost:3000")
                .AllowAnyHeader()
                .AllowAnyMethod()
                .AllowCredentials(); // Required for SignalR
        });
    });
}
```

### **3. Hub URL Convention**

ABP automatically maps hubs using this pattern:
```
/signalr-hubs/{hub-name-kebab-case}
```

Examples:
- `RoomStatusHub` → `/signalr-hubs/room-status`
- `MessagingHub` → `/signalr-hubs/messaging`
- `NotificationHub` → `/signalr-hubs/notification`

### **4. Frontend Connection (ABP Standard)**

```typescript
// Use ABP's conventional hub URL
const hubUrl = `${apiBaseUrl}/signalr-hubs/room-status`;

const connection = new HubConnectionBuilder()
    .withUrl(hubUrl, {
        accessTokenFactory: () => getAccessToken(),
        withCredentials: true,
    })
    .withAutomaticReconnect()
    .build();
```

## 🚀 **Complete Flow for Room Status Updates**

### **Backend Flow:**
1. **User performs check-in/check-out**
2. **ReservationDetailsAppService.UpdateRoomStatusAsync()** updates room status
3. **SignalR notification sent** via `IRoomStatusNotificationService`
4. **ABP automatically handles** hub routing and authentication
5. **All connected clients receive** the notification

### **Frontend Flow:**
1. **Connect to ABP hub** at `/signalr-hubs/room-status`
2. **ABP automatically adds** user to "RoomStatusUpdates" group
3. **Listen for "RoomStatusChanged"** events
4. **Update UI** when notifications received

## 📋 **ABP vs Standard SignalR Comparison**

| Feature | Standard SignalR | ABP SignalR |
|---------|------------------|-------------|
| Hub Registration | Manual `services.AddSignalR()` | Automatic |
| Hub Mapping | Manual `endpoints.MapHub<>()` | Automatic |
| Authentication | Manual setup | Integrated with ABP auth |
| Base Properties | None | `CurrentUser`, `Logger`, `L` |
| Routing | Manual configuration | Conventional `/signalr-hubs/` |
| Dependency Injection | Manual registration | Automatic |

## ✅ **Benefits of Following ABP Standards**

1. **Less Code**: No manual registration/mapping needed
2. **Consistent**: Follows ABP conventions
3. **Integrated**: Works seamlessly with ABP authentication
4. **Maintainable**: Standard patterns across the application
5. **Future-proof**: Compatible with ABP updates

## 🔧 **Advanced ABP SignalR Configuration**

### **Custom Hub Configuration**
```csharp
Configure<AbpSignalROptions>(options =>
{
    options.Hubs.AddOrUpdate(
        typeof(RoomStatusHub),
        config =>
        {
            config.RoutePattern = "/custom-room-status"; // Custom route
            config.ConfigureActions.Add(hubOptions =>
            {
                hubOptions.LongPolling.PollTimeout = TimeSpan.FromSeconds(30);
            });
        }
    );
});
```

### **Disable Auto Registration (if needed)**
```csharp
[DisableConventionalRegistration]
[DisableAutoHubMap]
public class CustomHub : AbpHub
{
    // Manual registration/mapping required
}
```

## 🎯 **Summary**

**Following ABP standards provides:**
- ✅ **Automatic hub registration and mapping**
- ✅ **Built-in authentication integration**
- ✅ **Conventional routing patterns**
- ✅ **Rich base class with useful properties**
- ✅ **Consistent with ABP architecture**
- ✅ **Less boilerplate code**
- ✅ **Better maintainability**

**Your hub URL will be:** `https://your-backend/signalr-hubs/room-status`

This implementation is fully compliant with ABP Framework standards and provides all the benefits of ABP's SignalR integration while maintaining compatibility with standard SignalR client libraries.
