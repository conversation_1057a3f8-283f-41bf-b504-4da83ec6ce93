# Excel Conditional Formatting Guide

## Overview

The Excel export functionality now supports conditional background color formatting for individual data cells based on their content values. This feature allows you to automatically apply different background colors to cells that meet specific criteria, making reports more visually informative and easier to analyze.

## Features

### Supported Condition Types

1. **Specific Value Matching**: Apply colors based on exact value matches
2. **Numeric Comparisons**: Apply colors based on numeric comparisons (greater than, less than, etc.)
3. **Text Pattern Matching**: Apply colors based on text patterns (contains, starts with, ends with)

### Supported Operators

#### For Specific Values
- `Equals`: Exact match
- `NotEquals`: Not equal to value

#### For Numeric Comparisons
- `GreaterThan`: Value > comparison value
- `GreaterThanOrEqual`: Value >= comparison value
- `LessThan`: Value < comparison value
- `LessThanOrEqual`: Value <= comparison value

#### For Text Patterns
- `Contains`: Text contains pattern
- `StartsWith`: Text starts with pattern
- `EndsWith`: Text ends with pattern
- `NotContains`: Text does not contain pattern

## Implementation

### Data Model Structure

The conditional formatting is implemented through the following DTOs:

```csharp
public class ConditionalFormattingRuleDto
{
    public ConditionalFormattingType ConditionType { get; set; }
    public ConditionalFormattingOperator Operator { get; set; }
    public string? ComparisonValue { get; set; }
    public string? TextPattern { get; set; }
    public string BackgroundColor { get; set; } // Hex color code
    public int Priority { get; set; } = 0; // Lower numbers = higher priority
    public bool CaseSensitive { get; set; } = false;
}
```

### Integration with Column Configuration

Conditional rules are added to the existing `ReportExcelColumnConfigDto`:

```csharp
public class ReportExcelColumnConfigDto
{
    public string ColumnName { get; set; }
    public ExcelCellType CellType { get; set; }
    public string NumberFormat { get; set; }
    public ReportExcelStyleDto Style { get; set; }
    public int Width { get; set; }
    public List<ConditionalFormattingRuleDto> ConditionalRules { get; set; } = new();
}
```

## Usage Examples

### Example 1: Status Column with Specific Value Matching

```csharp
new ReportExcelColumnConfigDto
{
    ColumnName = "Status",
    ConditionalRules = new List<ConditionalFormattingRuleDto>
    {
        new ConditionalFormattingRuleDto
        {
            ConditionType = ConditionalFormattingType.SpecificValue,
            Operator = ConditionalFormattingOperator.Equals,
            ComparisonValue = "Paid",
            BackgroundColor = "#D4EDDA", // Light green
            Priority = 1
        },
        new ConditionalFormattingRuleDto
        {
            ConditionType = ConditionalFormattingType.SpecificValue,
            Operator = ConditionalFormattingOperator.Equals,
            ComparisonValue = "Pending",
            BackgroundColor = "#FFF3CD", // Light yellow
            Priority = 2
        },
        new ConditionalFormattingRuleDto
        {
            ConditionType = ConditionalFormattingType.SpecificValue,
            Operator = ConditionalFormattingOperator.Equals,
            ComparisonValue = "Overdue",
            BackgroundColor = "#F8D7DA", // Light red
            Priority = 3
        }
    }
}
```

### Example 2: Amount Column with Numeric Comparisons

```csharp
new ReportExcelColumnConfigDto
{
    ColumnName = "Amount",
    CellType = ExcelCellType.Currency,
    ConditionalRules = new List<ConditionalFormattingRuleDto>
    {
        new ConditionalFormattingRuleDto
        {
            ConditionType = ConditionalFormattingType.NumericComparison,
            Operator = ConditionalFormattingOperator.GreaterThan,
            ComparisonValue = "1000",
            BackgroundColor = "#CCE5FF", // Light blue for high amounts
            Priority = 1
        },
        new ConditionalFormattingRuleDto
        {
            ConditionType = ConditionalFormattingType.NumericComparison,
            Operator = ConditionalFormattingOperator.LessThan,
            ComparisonValue = "0",
            BackgroundColor = "#FFCCCC", // Light red for negative amounts
            Priority = 2
        }
    }
}
```

### Example 3: Description Column with Text Pattern Matching

```csharp
new ReportExcelColumnConfigDto
{
    ColumnName = "Description",
    ConditionalRules = new List<ConditionalFormattingRuleDto>
    {
        new ConditionalFormattingRuleDto
        {
            ConditionType = ConditionalFormattingType.TextPattern,
            Operator = ConditionalFormattingOperator.Contains,
            TextPattern = "Error",
            BackgroundColor = "#F8D7DA", // Light red for errors
            Priority = 1,
            CaseSensitive = false
        },
        new ConditionalFormattingRuleDto
        {
            ConditionType = ConditionalFormattingType.TextPattern,
            Operator = ConditionalFormattingOperator.Contains,
            TextPattern = "Success",
            BackgroundColor = "#D4EDDA", // Light green for success
            Priority = 2,
            CaseSensitive = false
        }
    }
}
```

## Testing

### Test Endpoint

A test endpoint has been created to demonstrate the conditional formatting functionality:

```
POST /api/app/report/export/excel/conditional-formatting-test
```

This endpoint includes examples of all three condition types and can be used to test the functionality with your report data.

### Testing Steps

1. **Prepare Test Data**: Ensure your report contains columns with appropriate data for testing:
   - A "Status" column with values like "Paid", "Pending", "Overdue"
   - An "Amount" column with numeric values (positive, negative, various ranges)
   - A "Description" column with text containing words like "Error", "Success"

2. **Call the Test Endpoint**: Use the conditional formatting test endpoint with your report ID

3. **Verify Results**: Open the generated Excel file and verify that:
   - Status cells have appropriate background colors
   - Amount cells are colored based on their values
   - Description cells are colored based on text content

## Technical Details

### Rule Evaluation Order

- Rules are evaluated in priority order (lower numbers first)
- The first matching rule determines the cell's background color
- If no rules match, the cell uses the default column style

### Error Handling

- Invalid hex color codes are ignored gracefully
- Type conversion errors don't break the export process
- Null values are handled appropriately

### Performance Considerations

- Conditional formatting evaluation is performed during cell processing
- Rules are sorted by priority once per column for efficiency
- Color validation is performed to prevent invalid color codes

### Backward Compatibility

- Existing reports without conditional rules continue to work unchanged
- Default column styling is preserved when no conditional rules match
- All existing Excel export functionality remains intact

## Color Recommendations

### Status Colors
- **Success/Paid/Complete**: `#D4EDDA` (Light green)
- **Warning/Pending/In Progress**: `#FFF3CD` (Light yellow)
- **Error/Overdue/Failed**: `#F8D7DA` (Light red)
- **Info/Active**: `#CCE5FF` (Light blue)

### Numeric Value Colors
- **High values**: `#CCE5FF` (Light blue)
- **Low/Negative values**: `#FFCCCC` (Light red)
- **Zero values**: `#F8F9FA` (Light gray)
- **Target range**: `#D4EDDA` (Light green)

### Text Pattern Colors
- **Errors**: `#F8D7DA` (Light red)
- **Warnings**: `#FFF3CD` (Light yellow)
- **Success**: `#D4EDDA` (Light green)
- **Information**: `#CCE5FF` (Light blue)
