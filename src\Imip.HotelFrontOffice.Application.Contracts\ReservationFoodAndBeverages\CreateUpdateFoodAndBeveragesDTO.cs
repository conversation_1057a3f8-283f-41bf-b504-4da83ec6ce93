﻿using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.ReservationFoodAndBeverages;

public class CreateUpdateReservationFoodAndBeveragesDto : IEntityDto<Guid>
{
    // Id property is required for bulk updates
    public Guid Id { get; set; }

    [Required]
    public Guid ReservationDetailsId { get; set; }

    [Required]
    public Guid FoodAndBeverageId { get; set; }

    public Guid? PaymentStatusId { get; set; } = default!; // Optional property for payment status ID

    [Required]
    [Range(0, double.MaxValue, ErrorMessage = "Total price must be greater than or equal to 0")]
    public decimal TotalPrice { get; set; }

    [Required]
    [Range(1, int.MaxValue, ErrorMessage = "Quantity must be at least 1")]
    public int Quantity { get; set; }

    [Required]
    public DateTime TransactionDate { get; set; } = default!;
}