﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Master.Company;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.PaymentDetails;
using Imip.HotelFrontOffice.PaymentMethods;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.Payments;

public class PaymentsDto : AuditedEntityDto<Guid>
{
    public decimal? TotalAmount { get; set; }
    public decimal? VatRate { get; set; }
    public decimal? VatAmount { get; set; }
    public decimal? PaidAmount { get; set; }
    public decimal? GrantTotal { get; set; }
    public string? PaymentCode { get; set; }
    public Guid ReservationsId { get; set; }
    public Guid? PaymentMethodId { get; set; }
    public Guid StatusId { get; set; }
    public Guid? TaxId { get; set; }
    public Guid? SettlementCompanyId { get; set; }
    public bool IsActive { get; set; } = false;
    public Guid? PaidCompanyId { get; set; }
    public DateTime? TransactionDate { get; set; }
    public Guid ReservationDetailsId { get; set; }
    public string? ReservationCode { get; set; }
    public string? BookerName { get; set; }
    public string? GuestName { get; set; }
    public MasterStatusDto Status { get; set; } = default!;
    public CompanyDto PaidCompany { get; set; } = default!;
    public CompanyDto SettlementCompany { get; set; } = default!;
    public PaymentMethodDto PaymentMethod { get; set; } = default!;
    public List<PaymentDetailsDto>? PaymentDetails { get; set; }

    /// <summary>
    /// Collection of general payment attachments
    /// </summary>
    public List<PaymentAttachmentInfoDto>? PaymentAttachments { get; set; }

    /// <summary>
    /// Collection of reservation-related payment attachments
    /// </summary>
    public List<PaymentReservationAttachmentInfoDto>? ReservationAttachments { get; set; }

    /// <summary>
    /// Stream URL for the automatically generated invoice
    /// </summary>
    public string? InvoiceStreamUrl { get; set; }

    /// <summary>
    /// File name of the automatically generated invoice
    /// </summary>
    public string? InvoiceFileName { get; set; }

    /// <summary>
    /// File ID of the automatically generated invoice
    /// </summary>
    public Guid? InvoiceFileId { get; set; }

    /// <summary>
    /// Computed reservation code with payment order (e.g., RES-001-2)
    /// </summary>
    public string? ReservationCodeWithOrder { get; set; }
}