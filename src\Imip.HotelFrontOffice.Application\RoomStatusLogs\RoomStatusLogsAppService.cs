﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.RoomStatusLogs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.RoomStatusLogs;

[Route("api/app/room-status-logs")]
[Authorize(WismaAppPermissions.PolicyRoomStatus.Default)]
public class RoomStatusLogsAppService : CrudAppService<
        RoomStatusLog,
        RoomStatusLogsDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateRoomStatusLogsDto,
        CreateUpdateRoomStatusLogsDto
    >, IRoomStatusLogsAppService
{
    private readonly IRepository<RoomStatusLog, Guid> _repository;

    public RoomStatusLogsAppService(IRepository<RoomStatusLog, Guid> repository)
        : base(repository)
    {
        _repository = repository;
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyRoomStatus.View)]
    public override Task<PagedResultDto<RoomStatusLogsDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyRoomStatus.View)]
    public override Task<RoomStatusLogsDto> GetAsync(Guid id)
    {
        return base.GetAsync(id);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyRoomStatus.Create)]
    public override Task<RoomStatusLogsDto> CreateAsync(CreateUpdateRoomStatusLogsDto input)
    {
        return base.CreateAsync(input);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyRoomStatus.Edit)]
    public override Task<RoomStatusLogsDto> UpdateAsync(Guid id, CreateUpdateRoomStatusLogsDto input)
    {
        return base.UpdateAsync(id, input);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyRoomStatus.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }
}