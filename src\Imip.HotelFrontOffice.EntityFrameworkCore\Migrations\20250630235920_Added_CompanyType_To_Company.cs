﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class Added_CompanyType_To_Company : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "CompanyTypeId",
                table: "MasterCompany",
                type: "uniqueidentifier",
                nullable: true);


            migrationBuilder.CreateIndex(
                name: "IX_MasterCompany_CompanyTypeId",
                table: "MasterCompany",
                column: "CompanyTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_MasterCompany_MasterStatus_CompanyTypeId",
                table: "MasterCompany",
                column: "CompanyTypeId",
                principalTable: "MasterStatus",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MasterCompany_MasterStatus_CompanyTypeId",
                table: "MasterCompany");

            migrationBuilder.DropIndex(
                name: "IX_MasterCompany_CompanyTypeId",
                table: "MasterCompany");

            migrationBuilder.DropColumn(
                name: "CompanyTypeId",
                table: "MasterCompany");
        }
    }
}
