using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Imip.HotelFrontOffice.Reports;

/// <summary>
/// Service for processing data into pivot table format
/// </summary>
public interface IPivotDataProcessor
{
    /// <summary>
    /// Transform regular report data into pivot table format
    /// </summary>
    /// <param name="sourceData">Original report data</param>
    /// <param name="pivotConfig">Pivot configuration</param>
    /// <returns>Transformed pivot data</returns>
    Task<PivotDataResult> TransformToPivotAsync(ReportPreviewDto sourceData, ReportPivotConfigDto pivotConfig);

    /// <summary>
    /// Generate dynamic column headers based on pivot configuration
    /// </summary>
    /// <param name="sourceData">Original report data</param>
    /// <param name="pivotConfig">Pivot configuration</param>
    /// <returns>List of dynamic column headers</returns>
    Task<List<string>> GenerateDynamicColumnsAsync(ReportPreviewDto sourceData, ReportPivotConfigDto pivotConfig);

    /// <summary>
    /// Validate pivot configuration against source data
    /// </summary>
    /// <param name="sourceData">Original report data</param>
    /// <param name="pivotConfig">Pivot configuration to validate</param>
    /// <returns>Validation result</returns>
    Task<PivotValidationResult> ValidatePivotConfigAsync(ReportPreviewDto sourceData, ReportPivotConfigDto pivotConfig);
}

/// <summary>
/// Result of pivot data transformation
/// </summary>
public class PivotDataResult
{
    /// <summary>
    /// Transformed pivot data
    /// </summary>
    public ReportPreviewDto PivotData { get; set; } = default!;

    /// <summary>
    /// Dynamic column headers generated from pivot
    /// </summary>
    public List<string> DynamicColumns { get; set; } = new();

    /// <summary>
    /// Static row group columns
    /// </summary>
    public List<string> RowGroupColumns { get; set; } = new();

    /// <summary>
    /// Mapping of original column values to formatted headers
    /// </summary>
    public Dictionary<string, string> ColumnMapping { get; set; } = new();

    /// <summary>
    /// Total row data (if included)
    /// </summary>
    public Dictionary<string, object>? TotalsRow { get; set; }

    /// <summary>
    /// Metadata about the pivot transformation
    /// </summary>
    public PivotMetadata Metadata { get; set; } = new();
}

/// <summary>
/// Metadata about pivot transformation
/// </summary>
public class PivotMetadata
{
    /// <summary>
    /// Number of unique pivot column values
    /// </summary>
    public int PivotColumnCount { get; set; }

    /// <summary>
    /// Number of row groups
    /// </summary>
    public int RowGroupCount { get; set; }

    /// <summary>
    /// Total number of data points processed
    /// </summary>
    public int TotalDataPoints { get; set; }

    /// <summary>
    /// Aggregation type used
    /// </summary>
    public PivotAggregationType AggregationType { get; set; }

    /// <summary>
    /// Processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }
}

/// <summary>
/// Result of pivot configuration validation
/// </summary>
public class PivotValidationResult
{
    /// <summary>
    /// Whether the configuration is valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Validation error messages
    /// </summary>
    public List<string> ErrorMessages { get; set; } = new();

    /// <summary>
    /// Warning messages
    /// </summary>
    public List<string> WarningMessages { get; set; } = new();

    /// <summary>
    /// Suggested improvements
    /// </summary>
    public List<string> Suggestions { get; set; } = new();
}
