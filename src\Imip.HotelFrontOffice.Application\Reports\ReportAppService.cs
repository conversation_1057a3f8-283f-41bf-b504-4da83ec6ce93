using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Models;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.Reports;
using Imip.HotelFrontOffice.Repositories;
using Imip.HotelFrontOffice.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.ObjectMapping;

namespace Imip.HotelFrontOffice.PolicyReport;

[Route("api/app/report")]
[RemoteService(false)]
[Authorize(WismaAppPermissions.PolicyReport.Default)]
public class ReportAppService : PermissionCheckedCrudAppService<
    Report,
    ReportDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateReportDto,
    CreateUpdateReportDto
>, IReportAppService
{
    private readonly ILogger<ReportAppService> _logger;
    private readonly IReportExecutionService _reportExecutionService;
    private readonly IReportRepository _reportRepository;

    public ReportAppService(
        IRepository<Report, Guid> repository,
        IReportExecutionService reportExecutionService,
        IReportRepository reportRepository,
        ILogger<ReportAppService> logger,
        IPermissionChecker permissionChecker)
        : base(repository, permissionChecker)
    {
        _logger = logger;
        _reportExecutionService = reportExecutionService;
        _reportRepository = reportRepository;
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyReport.View)]
    public override Task<PagedResultDto<ReportDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyReport.View)]
    public override Task<ReportDto> GetAsync(Guid id)
    {
        return base.GetAsync(id);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyReport.Create)]
    public override Task<ReportDto> CreateAsync(CreateUpdateReportDto input)
    {
        return base.CreateAsync(input);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyReport.Edit)]
    public override Task<ReportDto> UpdateAsync(Guid id, CreateUpdateReportDto input)
    {
        return base.UpdateAsync(id, input);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyReport.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }

    [HttpPost("preview")]
    [Authorize(WismaAppPermissions.PolicyReport.View)]
    public async Task<ReportPreviewDto> PreviewReportAsync(ReportExecutionDto input)
    {
        return await _reportExecutionService.ExecuteReportAsync(input.ReportId, input.Parameters ?? new Dictionary<string, object>());
    }

    [HttpPost("export/csv")]
    [Authorize(WismaAppPermissions.PolicyReport.View)]
    public async Task<byte[]> ExportReportToCsvAsync(ReportExecutionDto input)
    {
        return await _reportExecutionService.ExportReportToCsvAsync(input.ReportId, input.Parameters ?? new Dictionary<string, object>());
    }

    [HttpPost("export/excel")]
    [Authorize(WismaAppPermissions.PolicyReport.View)]
    public async Task<byte[]> ExportReportToExcelAsync(ReportExecutionDto input)
    {
        return await _reportExecutionService.ExportReportToExcelAsync(input.ReportId, input.Parameters ?? new Dictionary<string, object>());
    }

    [Authorize(WismaAppPermissions.PolicyReport.View)]
    public async Task<byte[]> ExportReportToExcelWithCustomHeaderAsync(ReportExecutionDto input, ReportExcelHeaderDto customHeader)
    {
        return await _reportExecutionService.ExportReportToExcelWithCustomHeaderAsync(input.ReportId, input.Parameters ?? new Dictionary<string, object>(), customHeader);
    }

    [HttpGet("{reportId}/parameters")]
    [Authorize(WismaAppPermissions.PolicyReport.Default)]
    public async Task<List<ReportParameterDto>> GetReportParametersAsync(Guid reportId)
    {
        return await _reportExecutionService.GetReportParametersAsync(reportId);
    }

    [HttpGet("active")]
    [Authorize(WismaAppPermissions.PolicyReport.Default)]
    public async Task<List<ReportDto>> GetActiveReportsAsync()
    {
        var reports = await _reportRepository.GetActiveReportsAsync();
        return ObjectMapper.Map<List<Report>, List<ReportDto>>(reports);
    }

    [Authorize(WismaAppPermissions.PolicyReport.Default)]
    public async Task<ReportExcelHeaderDto> GetReportExcelHeaderConfigAsync(Guid reportId)
    {
        return await _reportExecutionService.GetReportExcelHeaderConfigAsync(reportId);
    }

    [HttpPut("{reportId}/excel-header")]
    [Authorize(WismaAppPermissions.PolicyReport.Edit)]
    public async Task UpdateReportExcelHeaderConfigAsync(Guid reportId, ReportExcelHeaderDto headerConfig)
    {
        await _reportExecutionService.UpdateReportExcelHeaderConfigAsync(reportId, headerConfig);
    }

    [HttpGet("{reportId}/pivot-config")]
    [Authorize(WismaAppPermissions.PolicyReport.Default)]
    public async Task<ReportPivotConfigDto> GetReportPivotConfigAsync(Guid reportId)
    {
        return await _reportExecutionService.GetReportPivotConfigAsync(reportId);
    }

    [HttpPut("{reportId}/pivot-config")]
    [Authorize(WismaAppPermissions.PolicyReport.Edit)]
    public async Task UpdateReportPivotConfigAsync(Guid reportId, ReportPivotConfigDto pivotConfig)
    {
        await _reportExecutionService.UpdateReportPivotConfigAsync(reportId, pivotConfig);
    }

    [HttpPost("{reportId}/validate-pivot-config")]
    [Authorize(WismaAppPermissions.PolicyReport.Default)]
    public async Task<PivotValidationResult> ValidateReportPivotConfigAsync(Guid reportId, ReportPivotConfigDto pivotConfig)
    {
        return await _reportExecutionService.ValidateReportPivotConfigAsync(reportId, pivotConfig);
    }
}
