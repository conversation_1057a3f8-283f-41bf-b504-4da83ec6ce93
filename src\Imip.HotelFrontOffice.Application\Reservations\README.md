# Dynamic Filtering for Reservations

This document provides examples of how to use the dynamic filtering functionality for reservations.

## Basic Usage

The `GetListAsync(GetReservationsInput input)` method in `ReservationsAppService` now supports dynamic filtering on related entities. You can filter on properties of the reservation itself as well as properties of related entities like `Status`, `Company`, `ReservationDetails`, etc.

To use dynamic filtering, use the `/api/app/reservations/filtered` endpoint with a `GetReservationsInput` object. This endpoint is specifically designed for advanced filtering capabilities.

## Query Parameter Format

When calling the API via HTTP, you can use the following query parameter format:

### Sorting Parameters
```
sort[0][field]=reservationCode
sort[0][desc]=true
```

You can add multiple sort criteria:
```
sort[0][field]=reservationCode
sort[0][desc]=false
sort[1][field]=arrivalDate
sort[1][desc]=true
```

### Filter Parameters
```
filterGroup[0][operator]=And
filterGroup[0][conditions][0][fieldName]=status.name
filterGroup[0][conditions][0][operator]=Contains
filterGroup[0][conditions][0][value]=Confirmed
```

You can add multiple filter conditions:
```
filterGroup[0][operator]=And
filterGroup[0][conditions][0][fieldName]=status.name
filterGroup[0][conditions][0][operator]=Contains
filterGroup[0][conditions][0][value]=Confirmed
filterGroup[0][conditions][1][fieldName]=bookerName
filterGroup[0][conditions][1][operator]=Contains
filterGroup[0][conditions][1][value]=John
```

### Standard Parameters
```
SkipCount=0
MaxResultCount=10
Sorting=creationTime desc
```

You can combine these parameters as needed. The `sort` parameters will take precedence over the `Sorting` parameter if both are provided.

### Complete Example
Here's a complete example of a URL with all parameters:

```
/api/app/reservations/filtered?sort[0][field]=reservationCode&sort[0][desc]=false&filterGroup[0][operator]=And&filterGroup[0][conditions][0][fieldName]=status.name&filterGroup[0][conditions][0][operator]=Contains&filterGroup[0][conditions][0][value]=Confirmed&SkipCount=0&MaxResultCount=10
```

## Example Requests

### Filter by Status Name

```json
{
  "maxResultCount": 10,
  "skipCount": 0,
  "filterGroup": {
    "operator": "And",
    "conditions": [
      {
        "fieldName": "status.name",
        "operator": "Contains",
        "value": "Confirmed"
      }
    ]
  }
}
```

### Filter by Check-In Date and Room Number

```json
{
  "maxResultCount": 10,
  "skipCount": 0,
  "filterGroup": {
    "operator": "And",
    "conditions": [
      {
        "fieldName": "reservationDetails.checkInDate",
        "operator": "GreaterThanOrEqual",
        "value": "2023-01-01"
      },
      {
        "fieldName": "reservationDetails.room.roomNumber",
        "operator": "Contains",
        "value": "101"
      }
    ]
  }
}
```

### Complex Filter with OR Conditions

```json
{
  "maxResultCount": 10,
  "skipCount": 0,
  "filterGroup": {
    "operator": "Or",
    "conditions": [
      {
        "fieldName": "bookerName",
        "operator": "Contains",
        "value": "John"
      },
      {
        "fieldName": "reservationDetails.guest.fullname",
        "operator": "Contains",
        "value": "Smith"
      }
    ]
  }
}
```

### Sorting Example

```json
{
  "maxResultCount": 10,
  "skipCount": 0,
  "sort": [
    {
      "field": "checkInDate",
      "desc": false
    },
    {
      "field": "reservationDetails.guest.fullname",
      "desc": true
    }
  ]
}
```

## Supported Filter Operators

- `Equals`
- `NotEquals`
- `Contains`
- `StartsWith`
- `EndsWith`
- `GreaterThan`
- `GreaterThanOrEqual`
- `LessThan`
- `LessThanOrEqual`
- `IsNull`
- `IsNotNull`

## Supported Logical Operators

- `And`
- `Or`

## Notes

- When filtering on collection properties (like `reservationDetails`), the filter will match if ANY item in the collection matches the condition.
- For nested properties, use dot notation (e.g., `reservationDetails.room.roomNumber`).
- Sorting can be applied to multiple fields with different directions.
