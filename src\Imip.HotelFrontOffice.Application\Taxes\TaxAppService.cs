using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Taxes;

[Route("api/app/tax")]
[Authorize(WismaAppPermissions.PolicyTax.Default)]
public class TaxAppService : CrudAppService<
    Tax,
    TaxDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateTaxDto,
    CreateUpdateTaxDto
>, ITaxAppService
{
    private readonly ILogger<TaxAppService> _logger;
    public TaxAppService(
        IRepository<Tax, Guid> repository,
        ILogger<TaxAppService> logger
    ) : base(repository)
    {
        _logger = logger;
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyTax.Default)]
    public override Task<PagedResultDto<TaxDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyTax.Default)]
    public override Task<TaxDto> GetAsync(Guid id)
    {
        return base.GetAsync(id);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyTax.Create)]
    public override async Task<TaxDto> CreateAsync(CreateUpdateTaxDto input)
    {
        await CheckCodeUniquenessAsync(input.Code);
        return await base.CreateAsync(input);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyTax.Edit)]
    public override async Task<TaxDto> UpdateAsync(Guid id, CreateUpdateTaxDto input)
    {
        await CheckCodeUniquenessAsync(input.Code, id);
        return await base.UpdateAsync(id, input);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyTax.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }

    private async Task CheckCodeUniquenessAsync(string code, Guid? id = null)
    {
        var query = await Repository.GetQueryableAsync();
        var existingTax = query.Where(t => t.Code == code);

        if (id.HasValue)
        {
            existingTax = existingTax.Where(t => t.Id != id.Value);
        }

        if (await AsyncExecuter.AnyAsync(existingTax))
        {
            throw new BusinessException(HotelFrontOfficeDomainErrorCodes.TaxCodeAlreadyExists)
                .WithData("Code", code);
        }
    }
}
