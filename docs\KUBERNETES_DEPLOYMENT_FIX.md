# Kubernetes Deployment Fix Summary

## Problem Analysis

The pod `imip-wisma-web-ccb957777-mxgvs` was failing with `CrashLoopBackOff` status and the error:
```
standard_init_linux.go:228: exec user process caused: no such file or directory
```

## Root Causes Identified

### 1. **Command Override Conflict**
- **Issue**: Kubernetes deployment was overriding the Dockerfile ENTRYPOINT with its own command
- **Original K8s Command**: 
  ```yaml
  command: ["/bin/bash"]
  args: ["-c", "cd /app && chmod 755 /app/entrypoint.sh && sed -i 's/\\r$//' /app/entrypoint.sh && bash /app/entrypoint.sh"]
  ```
- **Dockerfile ENTRYPOINT**: `["bash", "-c", "cd /app && exec bash /app/entrypoint.sh"]`
- **Problem**: The override was causing conflicts and the entrypoint wasn't being executed properly

### 2. **BOM (Byte Order Mark) in Script Files**
- **Issue**: Script files (`setup-skiasharp.sh`, `verify-skiasharp.sh`, `entrypoint-unix.sh`) contained BOM characters
- **Effect**: The shell couldn't properly interpret the shebang line `#!/bin/bash`
- **Error**: "no such file or directory" when trying to execute scripts

### 3. **Line Ending Issues**
- **Issue**: Scripts might have Windows line endings (CRLF) instead of Unix line endings (LF)
- **Effect**: Shell execution failures

### 4. **Missing Dependencies**
- **Issue**: `curl` and `wget` might not be available for healthchecks and script operations

## Fixes Applied

### 1. **Fixed Dockerfile.new**
- ✅ **Removed BOM handling**: Added proper BOM removal in the RUN command
  ```dockerfile
  RUN for script in /app/setup-skiasharp.sh /app/verify-skiasharp.sh /app/entrypoint.sh; do \
          if [ -f "$script" ]; then \
              # Remove BOM if present and fix line endings \
              sed -i '1s/^\xEF\xBB\xBF//' "$script" && \
              sed -i 's/\r$//' "$script" && \
              chmod 755 "$script"; \
          fi; \
      done
  ```
- ✅ **Simplified ENTRYPOINT**: Changed from complex bash command to simple entrypoint
  ```dockerfile
  ENTRYPOINT ["/app/entrypoint.sh"]
  ```
- ✅ **Added missing dependencies**: Added `wget` to the package installation

### 2. **Fixed Script Files**
- ✅ **Removed BOM**: Cleaned BOM characters from:
  - `setup-skiasharp.sh`
  - `verify-skiasharp.sh`
  - `entrypoint-unix.sh`

### 3. **Fixed Kubernetes Deployment**
- ✅ **Removed command override**: Removed the command and args that were overriding the Dockerfile ENTRYPOINT
  ```yaml
  # Before:
  command: ["/bin/bash"]
  args: ["-c", "cd /app && chmod 755 /app/entrypoint.sh && sed -i 's/\\r$//' /app/entrypoint.sh && bash /app/entrypoint.sh"]
  
  # After:
  # Remove command override to use Dockerfile ENTRYPOINT
  ```

## Expected Results

After applying these fixes:

1. **Container startup should succeed**: The entrypoint script should execute properly
2. **SkiaSharp libraries should be set up correctly**: The init containers and setup scripts should work
3. **Application should start**: The .NET application should launch successfully
4. **Health checks should pass**: The `/api/health/kubernetes` endpoint should respond

## Deployment Steps

1. **Rebuild the Docker image** with the fixed `Dockerfile.new`
2. **Apply the updated Kubernetes deployment** with the temporary command override
3. **Monitor the pod startup** to ensure it reaches `Running` status
4. **Check application logs** to verify successful startup

## Update: Persistent Issue Resolution

After the initial fixes, the error persisted. Additional investigation revealed:

### Additional Issues Found:
1. **Exec format error**: The direct execution of shell scripts was failing
2. **Base image compatibility**: Potential issues with script execution in the .NET 9.0 base image

### Additional Fixes Applied:

1. **Enhanced Dockerfile entrypoint**:
   - Changed from direct script execution to using bash explicitly
   - Added wrapper script using `/bin/sh` for guaranteed compatibility
   - Added fallback entrypoint script

2. **Temporary Kubernetes workaround**:
   - Restored command override with enhanced error handling
   - Added inline BOM removal and line ending fixes
   - Added fallback to direct .NET app execution if entrypoint is missing

### Temporary Kubernetes Command:
```yaml
command: ["/bin/sh"]
args:
  [
    "-c",
    "cd /app && if [ -f /app/entrypoint.sh ]; then chmod 755 /app/entrypoint.sh && sed -i 's/\\r$//' /app/entrypoint.sh && sed -i '1s/^\\xEF\\xBB\\xBF//' /app/entrypoint.sh && exec /bin/bash /app/entrypoint.sh; else echo 'Entrypoint not found, starting app directly'; exec dotnet Imip.HotelFrontOffice.Web.dll; fi",
  ]
```

This ensures the pod can start even if there are issues with the entrypoint script.

## Verification Commands

```bash
# Check pod status
kubectl get pods -n imip-wisma-dev-new

# Check pod logs
kubectl logs -n imip-wisma-dev-new <pod-name>

# Check application health
kubectl exec -n imip-wisma-dev-new <pod-name> -- curl -f http://localhost/api/health/kubernetes

# Describe pod for detailed information
kubectl describe pod -n imip-wisma-dev-new <pod-name>
```

## Additional Notes

- The GitLab CI is correctly using `Dockerfile.new` and copying the required script files
- The init containers for SkiaSharp setup should work properly with the fixed scripts
- The volume mounts for certificates and data protection keys are correctly configured
- The security context allows the container to run as root, which is necessary for library setup

## Files Modified

1. `src/Imip.HotelFrontOffice.Web/Dockerfile.new`
2. `src/Imip.HotelFrontOffice.Web/setup-skiasharp.sh`
3. `src/Imip.HotelFrontOffice.Web/verify-skiasharp.sh`
4. `k8s/dev/web-deployment.yaml`