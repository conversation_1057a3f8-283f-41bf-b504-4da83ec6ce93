using System;

namespace Imip.HotelFrontOffice.Attachments;

/// <summary>
/// DTO for file download
/// </summary>
public class FileDto
{
    /// <summary>
    /// The name of the file
    /// </summary>
    public string FileName { get; set; }

    /// <summary>
    /// The content type of the file
    /// </summary>
    public string ContentType { get; set; }

    /// <summary>
    /// The file content as a byte array
    /// </summary>
    public byte[] Content { get; set; }

    /// <summary>
    /// Creates a new FileDto
    /// </summary>
    public FileDto(string fileName, string contentType, byte[] content)
    {
        FileName = fileName;
        ContentType = contentType;
        Content = content;
    }
}