﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.RoomTypes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.ServiceTypes;

[Route("api/app/service-types")]
[Authorize(WismaAppPermissions.PolicyServiceType.Default)]
public class ServiceTypesAppService : CrudAppService<
        ServiceType,
        ServiceTypesDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateServiceTypesDto,
        CreateUpdateServiceTypesDto
    >, IServiceTypesAppService
{
    private readonly IRepository<ServiceType, Guid> _repository;
    public ServiceTypesAppService(IRepository<ServiceType, Guid> repository, IPermissionChecker permissionChecker)
        : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<IQueryable<ServiceType>> CreateFilteredQueryAsync(
        PagedAndSortedResultRequestDto input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.Status);
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyServiceType.View)]
    public override Task<PagedResultDto<ServiceTypesDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyServiceType.View)]
    public override async Task<ServiceTypesDto> GetAsync(Guid id)
    {
        // await CheckGetPolicyAsync();

        var query = await Repository.GetQueryableAsync();
        var serviceType = await query
            .Include(x => x.Status)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (serviceType is null)
        {
            throw new EntityNotFoundException(typeof(ServiceType), id);
        }

        return ObjectMapper.Map<ServiceType, ServiceTypesDto>(serviceType);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyServiceType.Create)]
    public override Task<ServiceTypesDto> CreateAsync(CreateUpdateServiceTypesDto input)
    {
        return base.CreateAsync(input);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyServiceType.Edit)]
    public override Task<ServiceTypesDto> UpdateAsync(Guid id, CreateUpdateServiceTypesDto input)
    {
        return base.UpdateAsync(id, input);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyServiceType.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }
}