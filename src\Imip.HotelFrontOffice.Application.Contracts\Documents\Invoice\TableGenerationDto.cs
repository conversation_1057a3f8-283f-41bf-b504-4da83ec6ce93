using System.Collections.Generic;

namespace Imip.HotelFrontOffice.Documents.Invoice;

/// <summary>
/// DTO for table properties
/// </summary>
public class TablePropertiesDto
{
    /// <summary>
    /// The table width
    /// </summary>
    public int Width { get; set; } = 5000;

    /// <summary>
    /// The table width type (Pct, Dxa, Auto)
    /// </summary>
    public string WidthType { get; set; } = "Pct";

    /// <summary>
    /// The table layout (Fixed, Auto)
    /// </summary>
    public string Layout { get; set; } = "Fixed";

    /// <summary>
    /// The cell spacing
    /// </summary>
    public int CellSpacing { get; set; } = 0;

    /// <summary>
    /// The border size
    /// </summary>
    public int BorderSize { get; set; } = 1;

    /// <summary>
    /// The default left cell margin in DXA units (1/20th of a point)
    /// </summary>
    public int DefaultCellLeftMargin { get; set; } = 108; // Default Word margin (~0.075 inch)

    /// <summary>
    /// The default right cell margin in DXA units (1/20th of a point)
    /// </summary>
    public int DefaultCellRightMargin { get; set; } = 108; // Default Word margin (~0.075 inch)

    /// <summary>
    /// The default top cell margin in DXA units (1/20th of a point)
    /// </summary>
    public int DefaultCellTopMargin { get; set; } = 0;

    /// <summary>
    /// The default bottom cell margin in DXA units (1/20th of a point)
    /// </summary>
    public int DefaultCellBottomMargin { get; set; } = 0;
}

/// <summary>
/// DTO for table generation with merged cells
/// </summary>
public class TableGenerationDto
{
    /// <summary>
    /// The table headers
    /// </summary>
    public List<TableHeaderDto> Headers { get; set; } = new();

    /// <summary>
    /// The table rows
    /// </summary>
    public List<TableRowDto> Rows { get; set; } = new();

    /// <summary>
    /// The table properties
    /// </summary>
    public TablePropertiesDto? TableProperties { get; set; }
}

/// <summary>
/// DTO for table header
/// </summary>
public class TableHeaderDto
{
    /// <summary>
    /// The header text
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// The column span
    /// </summary>
    public int ColSpan { get; set; } = 1;

    /// <summary>
    /// The width in percentage
    /// </summary>
    public double WidthPercentage { get; set; } = 0;
}

/// <summary>
/// DTO for table row
/// </summary>
public class TableRowDto
{
    /// <summary>
    /// The cells in the row
    /// </summary>
    public List<TableCellDto> Cells { get; set; } = new();
}

/// <summary>
/// DTO for table cell
/// </summary>
public class TableCellDto
{
    /// <summary>
    /// The cell text
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// The column span
    /// </summary>
    public int ColSpan { get; set; } = 1;

    /// <summary>
    /// The row span
    /// </summary>
    public int RowSpan { get; set; } = 1;

    /// <summary>
    /// Whether the cell is a header
    /// </summary>
    public bool IsHeader { get; set; } = false;

    /// <summary>
    /// The horizontal alignment
    /// </summary>
    public TableCellAlignment HorizontalAlignment { get; set; } = TableCellAlignment.Left;

    /// <summary>
    /// The vertical alignment
    /// </summary>
    public TableCellAlignment VerticalAlignment { get; set; } = TableCellAlignment.Center;

    /// <summary>
    /// Whether to apply bold formatting
    /// </summary>
    public bool IsBold { get; set; } = false;

    /// <summary>
    /// The width in percentage
    /// </summary>
    public double WidthPercentage { get; set; } = 0;

    /// <summary>
    /// The font size in points (optional)
    /// </summary>
    public int? FontSize { get; set; } = null;

    /// <summary>
    /// The left cell margin in DXA units (1/20th of a point). If null, uses table default.
    /// </summary>
    public int? LeftMargin { get; set; } = null;

    /// <summary>
    /// The right cell margin in DXA units (1/20th of a point). If null, uses table default.
    /// </summary>
    public int? RightMargin { get; set; } = null;

    /// <summary>
    /// The top cell margin in DXA units (1/20th of a point). If null, uses table default.
    /// </summary>
    public int? TopMargin { get; set; } = null;

    /// <summary>
    /// The bottom cell margin in DXA units (1/20th of a point). If null, uses table default.
    /// </summary>
    public int? BottomMargin { get; set; } = null;
}

/// <summary>
/// Enum for table cell alignment
/// </summary>
public enum TableCellAlignment
{
    /// <summary>
    /// Left alignment
    /// </summary>
    Left,

    /// <summary>
    /// Center alignment
    /// </summary>
    Center,

    /// <summary>
    /// Right alignment
    /// </summary>
    Right,

    /// <summary>
    /// Top alignment
    /// </summary>
    Top,

    /// <summary>
    /// Bottom alignment
    /// </summary>
    Bottom
}
