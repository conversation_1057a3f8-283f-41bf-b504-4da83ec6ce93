using System;
using System.Collections.Generic;

namespace Imip.HotelFrontOffice.Reports;

/// <summary>
/// Configuration for pivot table reports
/// </summary>
public class ReportPivotConfigDto
{
    /// <summary>
    /// Field to pivot on (creates dynamic columns)
    /// </summary>
    public string PivotColumnField { get; set; } = default!;

    /// <summary>
    /// Field to aggregate/sum in the pivot
    /// </summary>
    public string ValueField { get; set; } = default!;

    /// <summary>
    /// Fields that remain as rows (grouping fields)
    /// </summary>
    public List<string> RowGroupFields { get; set; } = new();

    /// <summary>
    /// Aggregation function to apply to the value field
    /// </summary>
    public PivotAggregationType AggregationType { get; set; } = PivotAggregationType.Sum;

    /// <summary>
    /// Format for dynamic column headers
    /// </summary>
    public PivotColumnHeaderFormat HeaderFormat { get; set; } = new();

    /// <summary>
    /// Whether to include totals row
    /// </summary>
    public bool IncludeTotalsRow { get; set; } = true;

    /// <summary>
    /// Whether to include totals column
    /// </summary>
    public bool IncludeTotalsColumn { get; set; } = true;

    /// <summary>
    /// Custom sorting for pivot columns
    /// </summary>
    public PivotColumnSorting ColumnSorting { get; set; } = new();

    /// <summary>
    /// Default value for empty cells
    /// </summary>
    public object? DefaultValue { get; set; } = 0;

    /// <summary>
    /// Cell formatting for pivot values
    /// </summary>
    public ExcelCellType ValueCellType { get; set; } = ExcelCellType.Number;

    /// <summary>
    /// Number format for pivot values
    /// </summary>
    public string? ValueNumberFormat { get; set; }
}

/// <summary>
/// Aggregation types for pivot values
/// </summary>
public enum PivotAggregationType
{
    Sum = 1,
    Count = 2,
    Average = 3,
    Min = 4,
    Max = 5,
    CountDistinct = 6
}

/// <summary>
/// Configuration for pivot column header formatting
/// </summary>
public class PivotColumnHeaderFormat
{
    /// <summary>
    /// Date format for date-based pivot columns
    /// </summary>
    public string? DateFormat { get; set; }

    /// <summary>
    /// Prefix for column headers
    /// </summary>
    public string? Prefix { get; set; }

    /// <summary>
    /// Suffix for column headers
    /// </summary>
    public string? Suffix { get; set; }

    /// <summary>
    /// Custom header mapping (original value -> display value)
    /// </summary>
    public Dictionary<string, string> CustomMapping { get; set; } = new();

    /// <summary>
    /// Maximum length for header text
    /// </summary>
    public int? MaxLength { get; set; }
}

/// <summary>
/// Configuration for pivot column sorting
/// </summary>
public class PivotColumnSorting
{
    /// <summary>
    /// Sort direction for pivot columns
    /// </summary>
    public PivotSortDirection Direction { get; set; } = PivotSortDirection.Ascending;

    /// <summary>
    /// Sort type for pivot columns
    /// </summary>
    public PivotSortType SortType { get; set; } = PivotSortType.Alphabetical;

    /// <summary>
    /// Custom sort order (for predefined column order)
    /// </summary>
    public List<string> CustomOrder { get; set; } = new();
}

/// <summary>
/// Sort direction for pivot columns
/// </summary>
public enum PivotSortDirection
{
    Ascending = 1,
    Descending = 2,
    Custom = 3
}

/// <summary>
/// Sort type for pivot columns
/// </summary>
public enum PivotSortType
{
    Alphabetical = 1,
    Numerical = 2,
    Date = 3,
    Custom = 4
}
