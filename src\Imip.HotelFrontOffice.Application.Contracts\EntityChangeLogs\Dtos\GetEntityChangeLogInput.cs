using System;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.EntityChangeLogs.Dtos
{
    /// <summary>
    /// Input DTO for getting entity change logs
    /// </summary>
    public class GetEntityChangeLogInput : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// The ID of the entity to get change logs for
        /// </summary>
        public string? EntityId { get; set; }

        /// <summary>
        /// The full type name of the entity to get change logs for
        /// </summary>
        public string? EntityTypeFullName { get; set; }

        /// <summary>
        /// Start time for filtering change logs
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// End time for filtering change logs
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// Type of change to filter by
        /// </summary>
        public EntityChangeTypeDto? ChangeType { get; set; }
    }
}
