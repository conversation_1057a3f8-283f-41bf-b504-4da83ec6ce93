import { useEffect, useRef, useState, useCallback } from 'react';
import { SignalRService, RoomStatusChangeNotification } from '../services/signalr-service';

interface UseSignalROptions {
  hubUrl: string;
  getAccessToken: () => Promise<string | null>;
  autoConnect?: boolean;
}

interface UseSignalRReturn {
  connectionState: string;
  isConnected: boolean;
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  joinRoomGroup: (roomId: string) => Promise<void>;
  leaveRoomGroup: (roomId: string) => Promise<void>;
  ping: () => Promise<void>;
  onRoomStatusChanged: (callback: (notification: RoomStatusChangeNotification) => void) => void;
  offRoomStatusChanged: (callback: (notification: RoomStatusChangeNotification) => void) => void;
}

export const useSignalR = ({
  hubUrl,
  getAccessToken,
  autoConnect = true,
}: UseSignalROptions): UseSignalRReturn => {
  const [connectionState, setConnectionState] = useState<string>('Disconnected');
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const signalRServiceRef = useRef<SignalRService | null>(null);

  // Initialize SignalR service
  useEffect(() => {
    signalRServiceRef.current = new SignalRService(hubUrl, getAccessToken);
  }, [hubUrl, getAccessToken]);

  // Monitor connection state
  useEffect(() => {
    const checkConnectionState = () => {
      if (signalRServiceRef.current) {
        const state = signalRServiceRef.current.connectionState;
        const connected = signalRServiceRef.current.isConnected;
        
        setConnectionState(state);
        setIsConnected(connected);
      }
    };

    // Check connection state every second
    const interval = setInterval(checkConnectionState, 1000);

    return () => clearInterval(interval);
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect && signalRServiceRef.current) {
      signalRServiceRef.current.connect();
    }

    // Cleanup on unmount
    return () => {
      if (signalRServiceRef.current) {
        signalRServiceRef.current.disconnect();
      }
    };
  }, [autoConnect]);

  const connect = useCallback(async () => {
    if (signalRServiceRef.current) {
      await signalRServiceRef.current.connect();
    }
  }, []);

  const disconnect = useCallback(async () => {
    if (signalRServiceRef.current) {
      await signalRServiceRef.current.disconnect();
    }
  }, []);

  const joinRoomGroup = useCallback(async (roomId: string) => {
    if (signalRServiceRef.current) {
      await signalRServiceRef.current.joinRoomGroup(roomId);
    }
  }, []);

  const leaveRoomGroup = useCallback(async (roomId: string) => {
    if (signalRServiceRef.current) {
      await signalRServiceRef.current.leaveRoomGroup(roomId);
    }
  }, []);

  const ping = useCallback(async () => {
    if (signalRServiceRef.current) {
      await signalRServiceRef.current.ping();
    }
  }, []);

  const onRoomStatusChanged = useCallback((callback: (notification: RoomStatusChangeNotification) => void) => {
    if (signalRServiceRef.current) {
      signalRServiceRef.current.onRoomStatusChanged(callback);
    }
  }, []);

  const offRoomStatusChanged = useCallback((callback: (notification: RoomStatusChangeNotification) => void) => {
    if (signalRServiceRef.current) {
      signalRServiceRef.current.offRoomStatusChanged(callback);
    }
  }, []);

  return {
    connectionState,
    isConnected,
    connect,
    disconnect,
    joinRoomGroup,
    leaveRoomGroup,
    ping,
    onRoomStatusChanged,
    offRoomStatusChanged,
  };
};
