using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.EntityChangeLogs.Dtos
{
    /// <summary>
    /// DTO for ABP audit logs with all related details
    /// </summary>
    public class AuditLogWithDetailsDto : EntityDto<Guid>
    {
        /// <summary>
        /// Application name
        /// </summary>
        public string? ApplicationName { get; set; }

        /// <summary>
        /// User ID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// Username
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// Tenant ID
        /// </summary>
        public Guid? TenantId { get; set; }

        /// <summary>
        /// Tenant name
        /// </summary>
        public string? TenantName { get; set; }

        /// <summary>
        /// Execution time
        /// </summary>
        public DateTime ExecutionTime { get; set; }

        /// <summary>
        /// Execution duration in milliseconds
        /// </summary>
        public int ExecutionDuration { get; set; }

        /// <summary>
        /// Client IP address
        /// </summary>
        public string? ClientIpAddress { get; set; }

        /// <summary>
        /// Browser info
        /// </summary>
        public string? BrowserInfo { get; set; }

        /// <summary>
        /// HTTP method
        /// </summary>
        public string? HttpMethod { get; set; }

        /// <summary>
        /// URL
        /// </summary>
        public string? Url { get; set; }

        /// <summary>
        /// HTTP status code
        /// </summary>
        public int? HttpStatusCode { get; set; }

        /// <summary>
        /// List of audit log actions
        /// </summary>
        public List<AuditLogActionDto> Actions { get; set; }

        /// <summary>
        /// List of entity changes
        /// </summary>
        public List<EntityChangeLogDto> EntityChanges { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public AuditLogWithDetailsDto()
        {
            Actions = new List<AuditLogActionDto>();
            EntityChanges = new List<EntityChangeLogDto>();
        }
    }

    /// <summary>
    /// DTO for ABP audit log actions
    /// </summary>
    public class AuditLogActionDto : EntityDto<Guid>
    {
        /// <summary>
        /// Tenant ID
        /// </summary>
        public Guid? TenantId { get; set; }

        /// <summary>
        /// Audit log ID
        /// </summary>
        public Guid AuditLogId { get; set; }

        /// <summary>
        /// Service name
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// Method name
        /// </summary>
        public string? MethodName { get; set; }

        /// <summary>
        /// Parameters
        /// </summary>
        public string? Parameters { get; set; }

        /// <summary>
        /// Execution time
        /// </summary>
        public DateTime ExecutionTime { get; set; }

        /// <summary>
        /// Execution duration in milliseconds
        /// </summary>
        public int ExecutionDuration { get; set; }
    }
}
