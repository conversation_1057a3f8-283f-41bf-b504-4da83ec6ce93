# Invoice Generation Usage Example

This document provides examples of how to use the invoice generation API.

## Prerequisites

Before using the invoice generation API, you need to:

1. Create a DOCX template for invoices (see [Invoice Template Structure](./InvoiceTemplateStructure.md))
2. Upload the template to the system as a document template with type "Invoice"
3. Have at least one payment record in the system

## API Endpoints

### Generate Invoice

**Endpoint:** `POST /api/invoice/generate`

**Request Body:**
```json
{
  "paymentId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "templateId": null,
  "includePaymentDetails": true,
  "useAdvancedTable": true,
  "generatePdf": true,
  "customFilename": "Invoice_123"
}
```

**Parameters:**
- `paymentId` (required): The ID of the payment to generate an invoice for
- `templateId` (optional): The ID of the specific template to use (if not provided, the default Invoice template will be used)
- `includePaymentDetails` (optional, default: true): Whether to include payment details in the invoice
- `useAdvancedTable` (optional, default: false): Whether to use the advanced table with merged cells (true) or simple table (false)
- `generatePdf` (optional, default: true): Whether to generate a PDF (true) or DOCX (false)
- `customFilename` (optional): Custom filename (without extension)

**Response:**
The API will return the generated file (PDF or DOCX) with the appropriate content type.

### Get Invoice Template Data

**Endpoint:** `GET /api/invoice/template-data/{paymentId}`

**Parameters:**
- `paymentId` (required): The ID of the payment to get template data for

**Response:**
```json
{
  "invoiceNumber": "INV-20230101-12345678",
  "invoiceDate": "2023-01-01T00:00:00",
  "paymentCode": "PAY-12345",
  "transactionDate": "2023-01-01T00:00:00",
  "reservationCode": "RES-12345",
  "bookerName": "John Doe",
  "guestName": "Jane Doe",
  "paymentMethod": "Credit Card",
  "paymentStatus": "Paid",
  "totalAmount": 1000.00,
  "paidAmount": 1000.00,
  "paymentDetails": [
    {
      "sourceType": "Room",
      "sourceId": "101",
      "description": "Room 101 - 1 night",
      "amount": 800.00
    },
    {
      "sourceType": "Service",
      "sourceId": "BRK",
      "description": "Breakfast",
      "amount": 200.00
    }
  ],
  "companyName": "Hotel Front Office",
  "companyAddress": "123 Main St, City, Country",
  "companyPhone": "+1234567890",
  "companyEmail": "<EMAIL>",
  "companyLogoUrl": "https://example.com/logo.png",
  "checkInDate": "2023-01-01T00:00:00",
  "checkOutDate": "2023-01-02T00:00:00",
  "roomNumber": "101"
}
```

## Code Examples

### C# Example

```csharp
// Inject the service
private readonly IInvoiceDocumentService _invoiceDocumentService;

public MyService(IInvoiceDocumentService invoiceDocumentService)
{
    _invoiceDocumentService = invoiceDocumentService;
}

// Generate an invoice
public async Task<FileDto> GenerateInvoiceAsync(Guid paymentId)
{
    var input = new InvoiceGenerationDto
    {
        PaymentId = paymentId,
        IncludePaymentDetails = true,
        UseAdvancedTable = true,  // Use the advanced table with merged cells
        GeneratePdf = true
    };

    return await _invoiceDocumentService.GenerateInvoiceAsync(input);
}

// Get invoice template data
public async Task<InvoiceTemplateDataDto> GetInvoiceTemplateDataAsync(Guid paymentId)
{
    return await _invoiceDocumentService.GetInvoiceTemplateDataAsync(paymentId);
}
```

### JavaScript Example

```javascript
// Generate an invoice
async function generateInvoice(paymentId) {
    const response = await fetch('/api/invoice/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            paymentId: paymentId,
            includePaymentDetails: true,
            useAdvancedTable: true,  // Use the advanced table with merged cells
            generatePdf: true
        })
    });

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Get the file as a blob
    const blob = await response.blob();

    // Create a URL for the blob
    const url = window.URL.createObjectURL(blob);

    // Create a link and click it to download the file
    const a = document.createElement('a');
    a.href = url;
    a.download = 'invoice.pdf';
    document.body.appendChild(a);
    a.click();
    a.remove();
}

// Get invoice template data
async function getInvoiceTemplateData(paymentId) {
    const response = await fetch(`/api/invoice/template-data/${paymentId}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
}
```

## Advanced Table Generation Example

The invoice generation API supports two table styles:

1. **Simple Table**: A basic table with columns for item number, description, and amount.
2. **Advanced Table**: A more complex table with merged cells (rowspan and colspan) that includes:
   - A quantity column
   - A notes section with rowspan
   - Subtotal, tax, and total rows

You can choose between these styles by setting the `useAdvancedTable` parameter in the request.

### How Merged Cells Work

The advanced table demonstrates two types of cell merging:

1. **Column Spanning (colspan)**: Cells that span multiple columns horizontally
   - Used for section headers, notes content, and total rows
   - Implemented using the `ColSpan` property in `TableCellDto`

2. **Row Spanning (rowspan)**: Cells that span multiple rows vertically
   - Used for the "Notes:" label that spans 3 rows
   - Implemented using the `RowSpan` property in `TableCellDto`

### Custom Table Generation

If you need more control over the table generation, you can use the `TableGenerator` class directly:

```csharp
// Create table data
var tableData = new TableGenerationDto
{
    Headers = new List<TableHeaderDto>
    {
        new TableHeaderDto { Text = "No.", WidthPercentage = 10 },
        new TableHeaderDto { Text = "Description", WidthPercentage = 60 },
        new TableHeaderDto { Text = "Amount", WidthPercentage = 30 }
    },
    Rows = new List<TableRowDto>()
};

// Add data rows
for (int i = 0; i < items.Count; i++)
{
    var item = items[i];
    tableData.Rows.Add(new TableRowDto
    {
        Cells = new List<TableCellDto>
        {
            new TableCellDto { Text = (i + 1).ToString() },
            new TableCellDto { Text = item.Description },
            new TableCellDto {
                Text = item.Amount.ToString("N2"),
                HorizontalAlignment = TableCellAlignment.Right
            }
        }
    });
}

// Add a row with column spanning
tableData.Rows.Add(new TableRowDto
{
    Cells = new List<TableCellDto>
    {
        new TableCellDto {
            Text = "Subtotal",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Right,
            ColSpan = 2
        },
        new TableCellDto {
            Text = subtotal.ToString("N2"),
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Right
        }
    }
});

// Add a row with row spanning
tableData.Rows.Add(new TableRowDto
{
    Cells = new List<TableCellDto>
    {
        new TableCellDto {
            Text = "Notes:",
            IsBold = true,
            RowSpan = 2,
            VerticalAlignment = TableCellAlignment.Top
        },
        new TableCellDto {
            Text = "First line of notes",
            ColSpan = 2
        }
    }
});

// Add another row that continues the row span
tableData.Rows.Add(new TableRowDto
{
    Cells = new List<TableCellDto>
    {
        // This cell is merged with the cell above
        new TableCellDto {
            Text = "Second line of notes",
            ColSpan = 2
        }
    }
});

// Replace the table placeholder in the document
TableGenerator.ReplaceTablePlaceholder(wordDocument, "{{TABLE}}", tableData);
```

This example demonstrates how to create a table with:
- Custom column widths
- Right-aligned numeric values
- Bold text for certain cells
- Column spanning (merging cells horizontally)
- Row spanning (merging cells vertically)
- Different vertical alignments
