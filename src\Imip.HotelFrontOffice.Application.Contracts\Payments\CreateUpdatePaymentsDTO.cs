﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Imip.HotelFrontOffice.PaymentDetails;
using Imip.HotelFrontOffice.PaymentGuests;

namespace Imip.HotelFrontOffice.Payments;

public class CreateUpdatePaymentsDto
{
    public decimal TotalAmount { get; set; } = default!;
    public decimal? PaidAmount { get; set; } = default!;
    public decimal? VatRate { get; set; } = default!;
    public decimal? VatAmount { get; set; } = default!;
    public decimal GrantTotal { get; set; } = default!;
    public string PaymentCode { get; set; } = default!;

    [Required]
    public DateTime TransactionDate { get; set; } = default!;

    [Required]
    public Guid ReservationsId { get; set; } = default!;

    [Required]
    public Guid PaymentMethodId { get; set; } = default!;

    [Required]
    public Guid StatusId { get; set; } = default!;

    public Guid? TaxId { get; set; } = default!;
    public Guid? PaidCompanyId { get; set; } = default!;
    public Guid? SettlementCompanyId { get; set; } = default!;
    public bool IsActive { get; set; } = false;

    // [Required]
    // public Guid ReservationDetailsId { get; set; } = default!;

    /// <summary>
    /// Collection of payment details to be created or updated with this payment
    /// </summary>
    public List<CreateUpdatePaymentDetailsDto>? PaymentDetails { get; set; }

    /// <summary>
    /// Collection of payment guests to be created or updated with this payment
    /// </summary>
    public List<CreateUpdatePaymentGuestsDto>? PaymentGuests { get; set; }

    /// <summary>
    /// Collection of general payment attachments to be created with this payment
    /// </summary>
    public List<PaymentAttachmentDto>? PaymentAttachments { get; set; }

    /// <summary>
    /// Collection of reservation-related payment attachments to be created with this payment
    /// </summary>
    public List<PaymentReservationAttachmentDto>? ReservationAttachments { get; set; }
}