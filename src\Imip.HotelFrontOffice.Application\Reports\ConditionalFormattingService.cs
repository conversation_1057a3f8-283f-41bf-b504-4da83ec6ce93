using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.Linq;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.Reports;

/// <summary>
/// Service for evaluating conditional formatting rules and determining cell background colors
/// </summary>
public class ConditionalFormattingService : ITransientDependency
{
    /// <summary>
    /// Evaluates conditional formatting rules for a cell value and returns the background color if any rule matches
    /// </summary>
    /// <param name="cellValue">The value of the cell to evaluate</param>
    /// <param name="rules">List of conditional formatting rules to evaluate</param>
    /// <returns>Hex color code if a rule matches, null otherwise</returns>
    public string? EvaluateConditionalFormatting(object? cellValue, List<ConditionalFormattingRuleDto> rules)
    {
        if (rules == null || rules.Count == 0)
            return null;

        // Sort rules by priority (lower numbers have higher priority)
        var sortedRules = rules.OrderBy(r => r.Priority).ToList();

        foreach (var rule in sortedRules)
        {
            if (EvaluateRule(cellValue, rule))
            {
                // Validate color format before returning
                if (IsValidHexColor(rule.BackgroundColor))
                {
                    return rule.BackgroundColor;
                }
            }
        }

        return null;
    }

    /// <summary>
    /// Evaluates a single conditional formatting rule against a cell value
    /// </summary>
    private bool EvaluateRule(object? cellValue, ConditionalFormattingRuleDto rule)
    {
        try
        {
            return rule.ConditionType switch
            {
                ConditionalFormattingType.SpecificValue => EvaluateSpecificValueRule(cellValue, rule),
                ConditionalFormattingType.NumericComparison => EvaluateNumericComparisonRule(cellValue, rule),
                ConditionalFormattingType.TextPattern => EvaluateTextPatternRule(cellValue, rule),
                _ => false
            };
        }
        catch
        {
            // Return false for any evaluation errors to prevent exceptions from breaking the export
            return false;
        }
    }

    /// <summary>
    /// Evaluates specific value matching rules
    /// </summary>
    private bool EvaluateSpecificValueRule(object? cellValue, ConditionalFormattingRuleDto rule)
    {
        if (string.IsNullOrEmpty(rule.ComparisonValue))
            return false;

        var cellString = cellValue?.ToString() ?? string.Empty;
        var comparisonString = rule.ComparisonValue;

        var stringComparison = rule.CaseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;

        return rule.Operator switch
        {
            ConditionalFormattingOperator.Equals => string.Equals(cellString, comparisonString, stringComparison),
            ConditionalFormattingOperator.NotEquals => !string.Equals(cellString, comparisonString, stringComparison),
            _ => false
        };
    }

    /// <summary>
    /// Evaluates numeric comparison rules
    /// </summary>
    private bool EvaluateNumericComparisonRule(object? cellValue, ConditionalFormattingRuleDto rule)
    {
        if (string.IsNullOrEmpty(rule.ComparisonValue))
            return false;

        // Try to parse both cell value and comparison value as decimals
        if (!TryParseDecimal(cellValue, out decimal cellDecimal) ||
            !TryParseDecimal(rule.ComparisonValue, out decimal comparisonDecimal))
        {
            return false;
        }

        return rule.Operator switch
        {
            ConditionalFormattingOperator.Equals => cellDecimal == comparisonDecimal,
            ConditionalFormattingOperator.NotEquals => cellDecimal != comparisonDecimal,
            ConditionalFormattingOperator.GreaterThan => cellDecimal > comparisonDecimal,
            ConditionalFormattingOperator.GreaterThanOrEqual => cellDecimal >= comparisonDecimal,
            ConditionalFormattingOperator.LessThan => cellDecimal < comparisonDecimal,
            ConditionalFormattingOperator.LessThanOrEqual => cellDecimal <= comparisonDecimal,
            _ => false
        };
    }

    /// <summary>
    /// Evaluates text pattern matching rules
    /// </summary>
    private bool EvaluateTextPatternRule(object? cellValue, ConditionalFormattingRuleDto rule)
    {
        if (string.IsNullOrEmpty(rule.TextPattern))
            return false;

        var cellString = cellValue?.ToString() ?? string.Empty;
        var pattern = rule.TextPattern;

        var stringComparison = rule.CaseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;

        return rule.Operator switch
        {
            ConditionalFormattingOperator.Contains => cellString.Contains(pattern, stringComparison),
            ConditionalFormattingOperator.StartsWith => cellString.StartsWith(pattern, stringComparison),
            ConditionalFormattingOperator.EndsWith => cellString.EndsWith(pattern, stringComparison),
            ConditionalFormattingOperator.NotContains => !cellString.Contains(pattern, stringComparison),
            _ => false
        };
    }

    /// <summary>
    /// Attempts to parse a value as a decimal, handling various numeric formats
    /// </summary>
    private bool TryParseDecimal(object? value, out decimal result)
    {
        result = 0;

        if (value == null)
            return false;

        // Handle direct decimal/numeric types
        if (value is decimal decimalValue)
        {
            result = decimalValue;
            return true;
        }

        if (value is int intValue)
        {
            result = intValue;
            return true;
        }

        if (value is double doubleValue)
        {
            result = (decimal)doubleValue;
            return true;
        }

        if (value is float floatValue)
        {
            result = (decimal)floatValue;
            return true;
        }

        // Try parsing string representation
        var stringValue = value.ToString();
        return decimal.TryParse(stringValue, NumberStyles.Number, CultureInfo.InvariantCulture, out result);
    }

    /// <summary>
    /// Validates if a string is a valid hex color code
    /// </summary>
    private bool IsValidHexColor(string? colorCode)
    {
        if (string.IsNullOrEmpty(colorCode))
            return false;

        try
        {
            // Try to parse as hex color
            ColorTranslator.FromHtml(colorCode);
            return true;
        }
        catch
        {
            return false;
        }
    }
}
