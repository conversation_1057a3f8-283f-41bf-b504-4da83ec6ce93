﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.Documents;
public class DocumentGenerationDto
{
    /// <summary>
    /// The ID of the payment to generate an invoice for
    /// </summary>
    [Required]
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Optional specific template ID to use (if not provided, the default Invoice template will be used)
    /// </summary>
    public Guid? TemplateId { get; set; }

    /// <summary>
    /// Whether to include payment details in the invoice
    /// </summary>
    public bool IncludeDetails { get; set; } = true;

    /// <summary>
    /// Whether to use the advanced table with merged cells (true) or simple table (false)
    /// </summary>
    public bool UseAdvancedTable { get; set; } = false;

    /// <summary>
    /// Whether to generate a PDF (true) or DOCX (false)
    /// </summary>
    public bool GeneratePdf { get; set; } = true;

    /// <summary>
    /// Optional custom filename (without extension)
    /// </summary>
    public string? CustomFilename { get; set; }
}
