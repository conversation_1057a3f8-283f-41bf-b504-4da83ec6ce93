import React, { useEffect, useState, useCallback } from 'react';
import { useSignalR } from '../hooks/useSignalR';
import { RoomStatusChangeNotification } from '../services/signalr-service';

interface Room {
  id: string;
  roomNumber: string;
  roomCode: string;
  roomStatus: {
    id: string;
    name: string;
    color: string;
    code: string;
  };
  lastModificationTime?: string;
  creationTime: string;
}

interface RoomListResponse {
  items: Room[];
  totalCount: number;
}

interface RoomListWithRealTimeUpdatesProps {
  apiBaseUrl: string;
  getAccessToken: () => Promise<string | null>;
}

export const RoomListWithRealTimeUpdates: React.FC<RoomListWithRealTimeUpdatesProps> = ({
  apiBaseUrl,
  getAccessToken,
}) => {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string>('');

  const signalRHubUrl = `${apiBaseUrl}/signalr-hubs/room-status`; // ABP standard hub route

  const {
    connectionState,
    isConnected,
    onRoomStatusChanged,
    offRoomStatusChanged,
  } = useSignalR({
    hubUrl: signalRHubUrl,
    getAccessToken,
    autoConnect: true,
  });

  // Handle real-time room status changes
  const handleRoomStatusChanged = useCallback((notification: RoomStatusChangeNotification) => {
    console.log('🔄 Room status changed:', notification);

    // Update the specific room in the list
    setRooms(prevRooms => 
      prevRooms.map(room => {
        if (room.id === notification.roomId) {
          return {
            ...room,
            roomStatus: {
              id: notification.newStatus.id,
              name: notification.newStatus.name,
              color: notification.newStatus.color,
              code: notification.newStatus.code,
            },
            lastModificationTime: notification.changeTimestamp,
          };
        }
        return room;
      })
    );

    // Update last update timestamp
    setLastUpdate(new Date().toLocaleTimeString());

    // Show toast notification (you can replace this with your preferred notification system)
    console.log(`✅ Room ${notification.roomNumber} status updated: ${notification.previousStatus?.name || 'Unknown'} → ${notification.newStatus.name}`);
  }, []);

  // Set up SignalR event listeners
  useEffect(() => {
    onRoomStatusChanged(handleRoomStatusChanged);

    return () => {
      offRoomStatusChanged(handleRoomStatusChanged);
    };
  }, [handleRoomStatusChanged, onRoomStatusChanged, offRoomStatusChanged]);

  // Fetch initial room data from /api/app/rooms
  const fetchRooms = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const token = await getAccessToken();
      
      const response = await fetch(`${apiBaseUrl}/api/app/rooms`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch rooms: ${response.statusText}`);
      }

      const data: RoomListResponse = await response.json();
      
      setRooms(data.items || []);
      setLastUpdate(new Date().toLocaleTimeString());
      
    } catch (err) {
      console.error('Error fetching rooms:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch rooms');
    } finally {
      setLoading(false);
    }
  }, [apiBaseUrl, getAccessToken]);

  // Initial data fetch
  useEffect(() => {
    fetchRooms();
  }, [fetchRooms]);

  // Manual refresh function
  const handleRefresh = () => {
    fetchRooms();
  };

  const getStatusColor = (room: Room) => {
    return room.roomStatus?.color || '#gray';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-lg">Loading rooms...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <strong>Error:</strong> {error}
        <button 
          onClick={handleRefresh}
          className="ml-4 px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header with connection status */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Room Status List</h1>
          <div className="flex items-center space-x-4">
            <div className={`px-3 py-1 rounded-full text-sm font-medium ${
              isConnected 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              SignalR: {connectionState}
            </div>
            <button
              onClick={handleRefresh}
              className="px-4 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
            >
              Refresh
            </button>
          </div>
        </div>
        
        {lastUpdate && (
          <p className="text-sm text-gray-600 mt-2">
            Last updated: {lastUpdate}
          </p>
        )}
      </div>

      {/* Room Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        {rooms.map((room) => (
          <div
            key={room.id}
            className="bg-white rounded-lg shadow-md p-4 border-l-4 transition-all duration-300 hover:shadow-lg"
            style={{ borderLeftColor: getStatusColor(room) }}
          >
            <div className="font-semibold text-gray-900 text-lg">{room.roomNumber}</div>
            <div className="text-sm text-gray-600 mb-2">{room.roomCode}</div>
            
            <div 
              className="px-2 py-1 rounded text-xs font-medium text-white text-center"
              style={{ backgroundColor: getStatusColor(room) }}
            >
              {room.roomStatus?.name || 'Unknown'}
            </div>
            
            {room.lastModificationTime && (
              <div className="text-xs text-gray-500 mt-2">
                Updated: {new Date(room.lastModificationTime).toLocaleString()}
              </div>
            )}
          </div>
        ))}
      </div>

      {rooms.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No rooms found
        </div>
      )}

      {/* Real-time status indicator */}
      {isConnected && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-3 py-2 rounded-full text-sm shadow-lg">
          🔄 Real-time updates active
        </div>
      )}
    </div>
  );
};
