using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Caching;

namespace Imip.HotelFrontOffice.Web.Services;

public static class DistributedCacheConfigurationService
{
    /// <summary>
    /// Configures distributed cache services with Redis or in-memory fallback
    /// </summary>
    public static void ConfigureDistributedCache(IServiceCollection services, IConfiguration configuration)
    {
        var redisIsEnabled = configuration.GetValue<bool>("Redis:IsEnabled", true);
        var redisConfiguration = configuration["Redis:Configuration"];

        ConfigureAbpDistributedCacheOptions(services);

        if (redisIsEnabled && !string.IsNullOrEmpty(redisConfiguration))
        {
            ConfigureRedisDistributedCache(redisConfiguration);
        }
        else
        {
            ConfigureInMemoryDistributedCache();
        }
    }

    private static void ConfigureAbpDistributedCacheOptions(IServiceCollection services)
    {
        services.Configure<AbpDistributedCacheOptions>(options =>
        {
            options.KeyPrefix = "Imip.HotelFrontOffice.Cache:";
            options.GlobalCacheEntryOptions.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(24);
            options.GlobalCacheEntryOptions.SlidingExpiration = TimeSpan.FromHours(1);
        });
    }

    private static void ConfigureRedisDistributedCache(string redisConfiguration)
    {
        try
        {
            // Redis cache will be configured by AbpCachingStackExchangeRedisModule
            Console.WriteLine("ABP distributed cache configured to use Redis");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Redis cache configuration failed: {ex.Message}. Using in-memory cache.");
            ConfigureInMemoryDistributedCache();
        }
    }

    private static void ConfigureInMemoryDistributedCache()
    {
        Console.WriteLine("Using in-memory distributed cache");
    }
}