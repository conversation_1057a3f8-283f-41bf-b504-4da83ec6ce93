# User Synchronization Implementation

This document describes the implementation of user synchronization between the Identity Server and the internal application database.

## Overview

The user synchronization system automatically creates and updates user records in the internal database when users authenticate via JWT tokens from the Identity Server. This ensures that user IDs remain consistent across both systems and that user data is always up-to-date.

## Architecture

### Components

1. **IUserSynchronizationService** - Service interface for user synchronization operations
2. **UserSynchronizationService** - Implementation of user synchronization logic
3. **UserSynchronizationMiddleware** - Middleware that intercepts API requests and synchronizes users
4. **TokenEndpointProxyMiddleware** - Enhanced to synchronize users after successful token authentication
5. **UserSynchronizationOptions** - Configuration options for the synchronization behavior

### Flow Diagram

```
JWT Token Request → Identity Server → Token Response → User Synchronization → Internal Database
                                                    ↓
API Request with JWT → Token Validation → User Synchronization → Continue Request Processing
```

## Implementation Details

### 1. User Synchronization Service

**Location**: `src\Imip.HotelFrontOffice.Application\Users\UserSynchronizationService.cs`

**Key Features**:
- Extracts user information from JWT token claims
- Creates new users or updates existing users in internal database
- Maintains ID consistency between Identity Server and internal database
- Synchronizes user roles and claims
- Handles errors gracefully without breaking the authentication flow

**Supported Claims**:
- `sub`, `user_id`, `NameIdentifier` → User ID
- `preferred_username`, `username`, `Name` → Username
- `email` → Email address
- `given_name`, `name` → First name
- `family_name`, `surname` → Last name
- `phone_number` → Phone number
- `email_verified` → Email confirmation status
- `phone_number_verified` → Phone confirmation status
- `role` → User roles
- `tenant_id` → Tenant ID (for multi-tenant scenarios)

### 2. Middleware Integration

**UserSynchronizationMiddleware**:
- Intercepts all API requests
- Extracts JWT tokens from Authorization headers
- Synchronizes users automatically
- Skips synchronization for static files and non-API endpoints

**TokenEndpointProxyMiddleware Enhancement**:
- Intercepts `/connect/token` requests
- Synchronizes users immediately after successful token authentication
- Ensures users are available in internal database before API usage

### 3. Configuration

**Location**: `src\Imip.HotelFrontOffice.Web\appsettings.json`

```json
{
  "UserSynchronization": {
    "IsEnabled": true,
    "UpdateExistingUsers": true,
    "SynchronizeRoles": true,
    "SynchronizeClaims": true,
    "EnableLogging": true
  }
}
```

**Configuration Options**:
- `IsEnabled`: Enable/disable user synchronization
- `UpdateExistingUsers`: Whether to update existing users during synchronization
- `SynchronizeRoles`: Whether to synchronize user roles
- `SynchronizeClaims`: Whether to synchronize user claims
- `EnableLogging`: Whether to log synchronization activities

## Usage Examples

### 1. Token-based Authentication Flow

```
1. Frontend sends credentials to `/connect/token`
2. Identity Server validates credentials and returns JWT token
3. TokenEndpointProxyMiddleware intercepts the response
4. User is automatically synchronized to internal database
5. Frontend receives token and user is ready for API calls
```

### 2. API Request Flow

```
1. Frontend sends API request with JWT token in Authorization header
2. UserSynchronizationMiddleware extracts token
3. If user doesn't exist in internal database, creates user from token claims
4. If user exists but data is outdated, updates user information
5. Request continues to API endpoint with synchronized user
```

## Error Handling

The synchronization system is designed to be resilient:

1. **Non-blocking**: Synchronization errors don't break the authentication flow
2. **Logging**: All errors are logged for debugging
3. **Graceful degradation**: API requests continue even if synchronization fails
4. **Retry logic**: Built-in retry mechanisms for transient failures

## Security Considerations

1. **ID Consistency**: User IDs from Identity Server are preserved exactly in internal database
2. **Data Validation**: All user data is validated before database operations
3. **Secure Claims**: Only trusted claims from verified JWT tokens are processed
4. **Audit Trail**: All synchronization activities are logged for audit purposes

## Performance Optimizations

1. **Conditional Updates**: Only updates users when data has actually changed
2. **Bulk Operations**: Uses efficient database operations for user creation/updates
3. **Caching**: Checks user existence before attempting synchronization
4. **Async Operations**: All database operations are asynchronous

## Testing

**Location**: `test\Imip.HotelFrontOffice.Application.Tests\Users\UserSynchronizationServiceTests.cs`

**Test Coverage**:
- User creation from JWT claims
- User updates when data changes
- User existence checks
- Error handling scenarios
- Configuration validation

## Monitoring and Logging

The system provides comprehensive logging:

```csharp
// User creation
_logger.LogInformation("Successfully synchronized user {UserId} ({UserName}) to internal database", 
    synchronizedUser.Id, synchronizedUser.UserName);

// User updates
_logger.LogDebug("Updated user {UserId} ({UserName}) properties", existingUser.Id, existingUser.UserName);

// Errors
_logger.LogError(ex, "Error synchronizing user from token response");
```

## Deployment Considerations

1. **Database Migrations**: Ensure ABP Identity tables are properly configured
2. **Configuration**: Update appsettings.json with appropriate synchronization settings
3. **Identity Server**: Ensure JWT tokens contain required user claims
4. **Monitoring**: Set up log monitoring for synchronization activities

## Troubleshooting

### Common Issues

1. **User not synchronized**: Check if JWT token contains required claims (`sub`, `preferred_username`)
2. **Synchronization disabled**: Verify `UserSynchronization:IsEnabled` is set to `true`
3. **Database errors**: Check database connectivity and ABP Identity table structure
4. **Token validation**: Ensure JWT tokens are valid and properly formatted

### Debug Steps

1. Enable detailed logging in appsettings.json
2. Check middleware order in pipeline
3. Verify JWT token claims using online JWT decoder
4. Monitor database for user creation/update operations
5. Check application logs for synchronization errors

## Future Enhancements

1. **Batch Synchronization**: Support for synchronizing multiple users at once
2. **Conflict Resolution**: Advanced conflict resolution for concurrent updates
3. **Custom Claims Mapping**: Configurable mapping between JWT claims and user properties
4. **Synchronization Metrics**: Performance metrics and monitoring dashboards
5. **Selective Synchronization**: Fine-grained control over which users to synchronize
