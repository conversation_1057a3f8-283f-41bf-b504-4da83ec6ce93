﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class Added_SettlementCompany : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsSettled",
                table: "AppPayments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "SettlementCompanyId",
                table: "AppPayments",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_SettlementCompanyId",
                table: "AppPayments",
                column: "SettlementCompanyId");

            migrationBuilder.AddForeignKey(
                name: "FK_AppPayments_MasterCompany_SettlementCompanyId",
                table: "AppPayments",
                column: "SettlementCompanyId",
                principalTable: "MasterCompany",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppPayments_MasterCompany_SettlementCompanyId",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_SettlementCompanyId",
                table: "AppPayments");

            migrationBuilder.DropColumn(
                name: "IsSettled",
                table: "AppPayments");

            migrationBuilder.DropColumn(
                name: "SettlementCompanyId",
                table: "AppPayments");
        }
    }
}
