﻿using System;

namespace Imip.HotelFrontOffice.Documents.RegistrationCard;
public class RegistrationCardDataDto
{
    public string? GuestName { get; set; } = string.Empty;
    public string? Gender { get; set; } = string.Empty;
    public string? Nationality { get; set; } = string.Empty;
    public DateOnly? DateOfBirthday { get; set; }
    public string? IdentityNumber { get; set; } = string.Empty;
    public string? Address { get; set; } = string.Empty;
    public int? MarketCode { get; set; }
    public DateTime? CheckInDate { get; set; }
    public DateTime? CheckOutDate { get; set; }
    public string? RoomNumber { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public decimal? Price { get; set; }
}
