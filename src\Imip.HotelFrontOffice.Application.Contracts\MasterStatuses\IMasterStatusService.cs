using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.MasterStatuses
{
    public interface IMasterStatusService : IApplicationService
    {
        /// <summary>
        /// Gets a list of MasterStatus DTOs filtered by DocType
        /// </summary>
        /// <param name="docType">The document type to filter by</param>
        /// <returns>A list of MasterStatus DTOs filtered by DocType</returns>
        Task<List<MasterStatusDto>> GetByDocTypeAsync(string docType);

        /// <summary>
        /// Gets a single MasterStatus DTO by ID and DocType
        /// </summary>
        /// <param name="id">The ID of the MasterStatus entity</param>
        /// <param name="docType">The document type to filter by</param>
        /// <returns>A MasterStatus DTO with the specified ID and DocType, or null if not found</returns>
        Task<MasterStatusDto?> GetByIdAndDocTypeAsync(Guid id, string docType);

        /// <summary>
        /// Gets a paged list of MasterStatus DTOs filtered by DocType
        /// </summary>
        /// <param name="input">The paging and sorting parameters</param>
        /// <param name="docType">The document type to filter by</param>
        /// <returns>A paged list of MasterStatus DTOs filtered by DocType</returns>
        Task<PagedResultDto<MasterStatusDto>> GetPagedListByDocTypeAsync(PagedAndSortedResultRequestDto input, string docType);
    }
}
