using System;
using System.IO;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Attachments;
using Volo.Abp;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.Documents;

/// <summary>
/// Interface for Syncfusion-based DOCX to PDF conversion service
/// </summary>
[RemoteService(false)] // Exclude from auto-API generation
public interface ISyncfusionDocxToPdfService : IApplicationService
{
    /// <summary>
    /// Converts a DOCX file to PDF using Syncfusion.DocIO
    /// </summary>
    /// <param name="docxBytes">The DOCX file content as byte array</param>
    /// <param name="filename">The filename without extension</param>
    /// <returns>A FileDto containing the PDF content</returns>
    Task<FileDto> ConvertDocxToPdfAsync(byte[] docxBytes, string filename);

    /// <summary>
    /// Converts a DOCX file to PDF using Syncfusion.DocIO
    /// </summary>
    /// <param name="docxStream">The DOCX file content as stream</param>
    /// <param name="filename">The filename without extension</param>
    /// <returns>A FileDto containing the PDF content</returns>
    Task<FileDto> ConvertDocxToPdfAsync(Stream docxStream, string filename);

    /// <summary>
    /// Converts a DOCX file to PDF using Syncfusion.DocIO
    /// </summary>
    /// <param name="docxFilePath">The path to the DOCX file</param>
    /// <param name="filename">The filename without extension (if null, the name from the path will be used)</param>
    /// <returns>A FileDto containing the PDF content</returns>
    Task<FileDto> ConvertDocxToPdfFromFileAsync(string docxFilePath, string? filename = null);
}
