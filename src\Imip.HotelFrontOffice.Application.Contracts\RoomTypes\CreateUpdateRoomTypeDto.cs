﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.RoomTypes;

public class CreateUpdateRoomTypeDto
{
    [Required]
    [StringLength(128)]
    public string Name { get; set; } = default!;

    [Required]
    public Guid? StatusId { get; set; } = default!;
    public string Alias { get; set; } = default!;

    // Dictionary for extra properties
    public Dictionary<string, object>? ExtraProperties { get; set; }
}