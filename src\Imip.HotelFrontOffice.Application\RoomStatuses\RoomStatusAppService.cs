﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Common;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.RoomStatuses;

[Route("api/app/room-status")]
[Authorize(WismaAppPermissions.PolicyRoomStatus.Default)]
public class RoomStatusAppService : CrudAppService<
        RoomStatus,
        RoomStatusDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateRoomStatusDto,
        CreateUpdateRoomStatusDto
    >, IRoomStatusAppService
{
    private readonly IRepository<RoomStatus, Guid> _repository;

    public RoomStatusAppService(IRepository<RoomStatus, Guid> repository,
        ILogger<RoomStatusAppService> logger)
        : base(repository)
    {
        _repository = repository;
    }
    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyRoomStatus.View)]
    public override Task<PagedResultDto<RoomStatusDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyRoomStatus.View)]
    public override Task<RoomStatusDto> GetAsync(Guid id)
    {
        return base.GetAsync(id);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyRoomStatus.Create)]
    public override Task<RoomStatusDto> CreateAsync(CreateUpdateRoomStatusDto input)
    {
        return base.CreateAsync(input);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyRoomStatus.Edit)]
    public override Task<RoomStatusDto> UpdateAsync(Guid id, CreateUpdateRoomStatusDto input)
    {
        return base.UpdateAsync(id, input);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyRoomStatus.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }

}