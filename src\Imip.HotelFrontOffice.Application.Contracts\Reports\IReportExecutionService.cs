using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Imip.HotelFrontOffice.Reports;

public interface IReportExecutionService
{
    Task<ReportPreviewDto> ExecuteReportAsync(Guid reportId, Dictionary<string, object> parameters);
    Task<byte[]> ExportReportToCsvAsync(Guid reportId, Dictionary<string, object> parameters);
    Task<byte[]> ExportReportToExcelAsync(Guid reportId, Dictionary<string, object> parameters);
    Task<byte[]> ExportReportToExcelWithCustomHeaderAsync(Guid reportId, Dictionary<string, object> parameters, ReportExcelHeaderDto customHeader);
    Task<List<ReportParameterDto>> GetReportParametersAsync(Guid reportId);
    Task<ReportExcelHeaderDto> GetReportExcelHeaderConfigAsync(Guid reportId);
    Task UpdateReportExcelHeaderConfigAsync(Guid reportId, ReportExcelHeaderDto headerConfig);
    Task<ReportPivotConfigDto> GetReportPivotConfigAsync(Guid reportId);
    Task UpdateReportPivotConfigAsync(Guid reportId, ReportPivotConfigDto pivotConfig);
    Task<PivotValidationResult> ValidateReportPivotConfigAsync(Guid reportId, ReportPivotConfigDto pivotConfig);
}
