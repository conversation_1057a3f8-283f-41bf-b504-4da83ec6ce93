using System;
using Imip.HotelFrontOffice.MasterStatuses;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.Master.Company;

public class CompanyDto : AuditedEntityDto<Guid>
{
    public string? Name { get; set; }
    public string? Address { get; set; }
    public string? Alias { get; set; }
    public string? ReservationCodePrefix { get; set; }
    public Guid? CompanyTypeId { get; set; }

    public MasterStatusDto CompanyType { get; set; } = default!;
}