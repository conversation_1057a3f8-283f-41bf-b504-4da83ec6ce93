﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Application.Contracts;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.Uow;

namespace Imip.HotelFrontOffice.ReportRooms;

[Authorize(WismaAppPermissions.PolicyGuest.Default)]
public class ReportRoomsAppService : ApplicationService, IReportRoomsAppService
{
    private readonly IDbContextProvider<HotelFrontOfficeDbContext> _dbContextProvider;
    private readonly ILogger<ReportRoomsAppService> _logger;

    public ReportRoomsAppService(
        IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider,
        ILogger<ReportRoomsAppService> logger)
    {
        _dbContextProvider = dbContextProvider;
        _logger = logger;
    }
    
    [UnitOfWork(IsDisabled = true)]
    public async Task<List<ReportRoomsDTO>> GetReportRoomsAsync(DateTime date)
    {
        try
        {
            // Get connection string from DbContext
            var dbContext = await _dbContextProvider.GetDbContextAsync();
            var connectionString = dbContext.Database.GetConnectionString();
            
            // Create a new connection to avoid transaction conflicts
            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();
            
            _logger.LogInformation("Executing sp_GetRoomTypesReportByDate with date: {Date}", date);
            
            using var command = connection.CreateCommand();
            command.CommandText = "sp_GetRoomTypesReportByDate";
            command.CommandType = CommandType.StoredProcedure;
            
            var parameter = command.CreateParameter();
            parameter.ParameterName = "@Date";
            parameter.Value = date;
            command.Parameters.Add(parameter);
            
            var result = new List<ReportRoomsDTO>();
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                result.Add(new ReportRoomsDTO
                {
                    Alias = reader["Alias"].ToString(),
                    Total = Convert.ToInt32(reader["Total"]),
                    Mtc = Convert.ToInt32(reader["Mtc"]),
                    Ls = reader["Ls"] != DBNull.Value ? Convert.ToInt32(reader["Ls"]) : 0,
                    Semua_tamu = Convert.ToInt32(reader["Semua_tamu"]),
                    Sisa = Convert.ToInt32(reader["Sisa"]),
                    Kosong = Convert.ToInt32(reader["Kosong"])
                });
            }
            
            _logger.LogInformation("Successfully retrieved {Count} report items", result.Count);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing sp_GetRoomTypesReportByDate: {Message}", ex.Message);
            throw;
        }
    }
    
    public async Task<List<ReportRoomsDTO>> GetRoomTypesReportByDateAsync(DateTime date)
    {
        return await GetReportRoomsAsync(date);
    }
}